/**
 * SCRIPT DI EMERGENZA - CORREZIONE TUTTI GLI ERRORI CONSOLE.LOG
 * Corregge tutti i console.log malformati creati dal script precedente
 */

const fs = require('fs');
const path = require('path');

class ConsoleErrorFixer {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.fixedFiles = 0;
    this.totalErrors = 0;
  }

  log(message, color = 'reset') {
    const colors = {
      green: '\x1b[32m',
      red: '\x1b[31m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m',
      bold: '\x1b[1m'
    };
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
    let files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!item.startsWith('.') && item !== 'node_modules') {
            files = files.concat(this.findFiles(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      this.log(`❌ Errore lettura directory ${dir}: ${error.message}`, 'red');
    }
    
    return files;
  }

  fixFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const originalContent = content;
      let errorsFixed = 0;

      // Pattern 1: console.log({ all'interno di oggetti
      // Esempio: property: value, console.log({ property: value,
      content = content.replace(/,\s*console\.log\(\{\s*([^}]+),?\s*\}/g, (match, captured) => {
        errorsFixed++;
        return '';
      });

      // Pattern 2: console.log({ seguito da proprietà duplicate
      // Esempio: console.log({ property: value,
      content = content.replace(/console\.log\(\{\s*([^}]+),?\s*/g, (match, captured) => {
        errorsFixed++;
        return '';
      });

      // Pattern 3: console.log({ vuoti
      // Esempio: console.log({ },
      content = content.replace(/console\.log\(\{\s*\},?\s*/g, () => {
        errorsFixed++;
        return '';
      });

      // Pattern 4: console.log({ in mezzo a oggetti JSX style
      // Esempio: style={{ prop: value, console.log({ prop: value,
      content = content.replace(/(\w+):\s*([^,}]+),\s*console\.log\(\{\s*\1:\s*\2,?/g, (match, prop, value) => {
        errorsFixed++;
        return `${prop}: ${value}`;
      });

      // Pattern 5: Rimuovi righe orfane che iniziano con proprietà senza oggetto
      // Esempio: property: value, (senza console.log davanti)
      const lines = content.split('\n');
      const fixedLines = [];
      let inObject = false;
      let braceCount = 0;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmed = line.trim();
        
        // Conta le parentesi graffe per capire se siamo in un oggetto
        braceCount += (line.match(/\{/g) || []).length;
        braceCount -= (line.match(/\}/g) || []).length;
        
        // Se la riga sembra una proprietà orfana (senza console.log)
        if (trimmed.match(/^\w+:\s*.+,?\s*$/) && braceCount > 0) {
          // Controlla se è una duplicazione della riga precedente
          const prevLine = i > 0 ? lines[i-1].trim() : '';
          if (prevLine.includes(trimmed.split(':')[0])) {
            errorsFixed++;
            continue; // Salta questa riga duplicata
          }
        }
        
        fixedLines.push(line);
      }
      
      content = fixedLines.join('\n');

      // Pattern 6: Pulisci virgole doppie e spazi extra
      content = content.replace(/,\s*,/g, ',');
      content = content.replace(/\{\s*,/g, '{');
      content = content.replace(/,\s*\}/g, '}');

      if (errorsFixed > 0) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.fixedFiles++;
        this.totalErrors += errorsFixed;
        this.log(`✅ ${path.relative(this.srcDir, filePath)}: ${errorsFixed} errori corretti`, 'green');
      }

      return errorsFixed;
    } catch (error) {
      this.log(`❌ Errore correzione ${filePath}: ${error.message}`, 'red');
      return 0;
    }
  }

  run() {
    this.log('\n🚨 CORREZIONE EMERGENZA ERRORI CONSOLE.LOG', 'bold');
    this.log('Corregge tutti i console.log malformati nel progetto', 'yellow');
    
    const files = this.findFiles(this.srcDir);
    this.log(`\n📁 Analizzando ${files.length} file...`, 'blue');
    
    files.forEach(file => {
      this.fixFile(file);
    });
    
    this.log('\n📊 RIEPILOGO CORREZIONI', 'bold');
    this.log('='.repeat(50), 'blue');
    this.log(`✅ File corretti: ${this.fixedFiles}`, 'green');
    this.log(`🔧 Errori risolti: ${this.totalErrors}`, 'green');
    
    if (this.totalErrors === 0) {
      this.log('\n✅ Nessun errore console.log trovato!', 'green');
    } else {
      this.log('\n🎯 Tutti gli errori console.log sono stati corretti!', 'green');
      this.log('Il sistema dovrebbe ora compilare correttamente.', 'blue');
    }
  }
}

// Esecuzione dello script
if (require.main === module) {
  const fixer = new ConsoleErrorFixer();
  fixer.run();
}

module.exports = ConsoleErrorFixer;
