# 🚀 GUIDA OTTIMIZZAZIONI PERFORMANCE

Questa guida fornisce istruzioni dettagliate per implementare le 410 ottimizzazioni identificate dai crash test.

---

## 🔥 PRIORITÀ CRITICA - IMPLEMENTARE SUBITO

### 1. 🔍 DEBOUNCING PER INPUT DI RICERCA

**Problema:** 81 input senza debouncing causano lag e troppe chiamate API.

#### Implementazione Hook useDebounce

Creare `src/hooks/useDebounce.ts`:

```typescript
import { useState, useEffect } from 'react';

export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};
```

#### Utilizzo negli Input di Ricerca

**Prima (❌ Problematico):**
```typescript
const [searchTerm, setSearchTerm] = useState('');

// Causa troppe chiamate API
useEffect(() => {
  fetchData(searchTerm);
}, [searchTerm]);

<Input 
  onChange={(e) => setSearchTerm(e.target.value)}
  placeholder="Cerca..."
/>
```

**Dopo (✅ Ottimizzato):**
```typescript
const [searchTerm, setSearchTerm] = useState('');
const debouncedSearchTerm = useDebounce(searchTerm, 300);

// Chiamata API solo dopo 300ms di inattività
useEffect(() => {
  if (debouncedSearchTerm) {
    fetchData(debouncedSearchTerm);
  }
}, [debouncedSearchTerm]);

<Input 
  onChange={(e) => setSearchTerm(e.target.value)}
  placeholder="Cerca..."
/>
```

#### File da Aggiornare (Priorità):
1. `app/cantieri/page.tsx` - Input ricerca cantieri
2. `app/cavi/page.tsx` - Filtri cavi
3. `app/parco-cavi/page.tsx` - Ricerca bobine
4. `components/cavi/CaviFilters.tsx` - Tutti i filtri
5. `components/cavi/SmartCaviFilter.tsx` - Filtro intelligente

---

## ⚡ PRIORITÀ ALTA - LAZY LOADING

### 2. 📦 LAZY LOADING COMPONENTI PESANTI

**Problema:** 33 componenti pesanti caricati sempre, anche quando non necessari.

#### Implementazione Lazy Loading

**Prima (❌ Problematico):**
```typescript
import HeavyDialog from './HeavyDialog';
import ComplexChart from './ComplexChart';

function MyComponent() {
  const [showDialog, setShowDialog] = useState(false);
  
  return (
    <div>
      {showDialog && <HeavyDialog />}
      <ComplexChart />
    </div>
  );
}
```

**Dopo (✅ Ottimizzato):**
```typescript
import { lazy, Suspense } from 'react';

const HeavyDialog = lazy(() => import('./HeavyDialog'));
const ComplexChart = lazy(() => import('./ComplexChart'));

function MyComponent() {
  const [showDialog, setShowDialog] = useState(false);
  
  return (
    <div>
      {showDialog && (
        <Suspense fallback={<div>Caricamento dialog...</div>}>
          <HeavyDialog />
        </Suspense>
      )}
      <Suspense fallback={<div>Caricamento grafico...</div>}>
        <ComplexChart />
      </Suspense>
    </div>
  );
}
```

#### Componenti da Convertire (Priorità):
1. **Dialog Pesanti:**
   - `InserisciMetriDialog`
   - `ModificaBobinaDialog`
   - `CreaComandaDialog`
   - `ImportExcelDialog`
   - `ExportDataDialog`

2. **Componenti Complessi:**
   - `CaviTable` (quando ha molti dati)
   - `BobineStatistics`
   - `CaviStatistics`

---

## 🧠 PRIORITÀ MEDIA - MEMOIZATION

### 3. 💾 USEMEMO PER CALCOLI COSTOSI

**Problema:** 9 calcoli costosi eseguiti ad ogni render.

#### Implementazione useMemo

**Prima (❌ Problematico):**
```typescript
function CaviPage() {
  const [cavi, setCavi] = useState([]);
  
  // Ricalcolato ad ogni render!
  const statistics = {
    total: cavi.length,
    installed: cavi.filter(c => c.stato === 'installato').length,
    pending: cavi.filter(c => c.stato === 'da_installare').length,
    totalMeters: cavi.reduce((sum, c) => sum + c.metri_teorici, 0)
  };
  
  return <div>{/* UI */}</div>;
}
```

**Dopo (✅ Ottimizzato):**
```typescript
import { useMemo } from 'react';

function CaviPage() {
  const [cavi, setCavi] = useState([]);
  
  // Ricalcolato solo quando cavi cambia
  const statistics = useMemo(() => ({
    total: cavi.length,
    installed: cavi.filter(c => c.stato === 'installato').length,
    pending: cavi.filter(c => c.stato === 'da_installare').length,
    totalMeters: cavi.reduce((sum, c) => sum + c.metri_teorici, 0)
  }), [cavi]);
  
  return <div>{/* UI */}</div>;
}
```

#### File da Ottimizzare:
1. `app/cavi/page.tsx` - Statistiche cavi
2. `app/reports/page.tsx` - Calcoli report
3. `components/cavi/CaviStatistics.tsx` - Statistiche
4. `components/bobine/BobineStatistics.tsx` - Statistiche bobine

---

## 🔄 PRIORITÀ MEDIA - EVITARE RE-RENDERS

### 4. 🚫 ELIMINARE OGGETTI/ARRAY INLINE

**Problema:** 253 oggetti/array creati inline causano re-render inutili.

#### Spostare Oggetti Fuori dal Render

**Prima (❌ Problematico):**
```typescript
function MyComponent({ data }) {
  return (
    <div>
      <Button style={{margin: 10, padding: 5}} />
      <List items={[1, 2, 3]} />
      <Component config={{theme: 'dark', size: 'large'}} />
    </div>
  );
}
```

**Dopo (✅ Ottimizzato):**
```typescript
// Spostare fuori dal componente
const buttonStyle = {margin: 10, padding: 5};
const listItems = [1, 2, 3];
const componentConfig = {theme: 'dark', size: 'large'};

function MyComponent({ data }) {
  return (
    <div>
      <Button style={buttonStyle} />
      <List items={listItems} />
      <Component config={componentConfig} />
    </div>
  );
}
```

#### Usare useMemo per Oggetti Dinamici

```typescript
function MyComponent({ theme, size }) {
  const config = useMemo(() => ({
    theme,
    size,
    timestamp: Date.now()
  }), [theme, size]);
  
  return <Component config={config} />;
}
```

---

## 📦 PRIORITÀ BASSA - REFACTORING COMPONENTI

### 5. ✂️ DIVIDERE COMPONENTI GRANDI

**Problema:** 34 componenti >300 linee sono difficili da mantenere.

#### Strategia di Refactoring

**Componenti da Dividere:**
1. `app/cantieri/page.tsx` (784 linee)
2. `app/cavi/page.tsx` (643 linee)
3. `app/comande/page.tsx` (518 linee)
4. `app/admin/page.tsx` (425 linee)

#### Esempio Refactoring

**Prima (❌ Monolitico):**
```typescript
// 500+ linee in un file
function CaviPage() {
  // Stato (50 linee)
  // Funzioni (200 linee)
  // Effetti (100 linee)
  // Render (200+ linee)
}
```

**Dopo (✅ Modulare):**
```typescript
// Dividere in:
// 1. Hook personalizzato per logica
function useCaviLogic() {
  // Stato e logica (150 linee)
}

// 2. Componenti più piccoli
function CaviFilters() { /* 50 linee */ }
function CaviTable() { /* 100 linee */ }
function CaviActions() { /* 50 linee */ }

// 3. Componente principale semplificato
function CaviPage() {
  const logic = useCaviLogic();
  
  return (
    <div>
      <CaviFilters />
      <CaviTable />
      <CaviActions />
    </div>
  ); // 50 linee totali
}
```

---

## 🛠️ IMPLEMENTAZIONE GRADUALE

### Settimana 1: Debouncing (CRITICO)
- [ ] Creare hook `useDebounce`
- [ ] Aggiornare input di ricerca principali
- [ ] Testare performance migliorata

### Settimana 2: Lazy Loading (ALTO)
- [ ] Convertire dialog pesanti
- [ ] Implementare Suspense boundaries
- [ ] Testare caricamento componenti

### Settimana 3: Memoization (MEDIO)
- [ ] Aggiungere useMemo ai calcoli costosi
- [ ] Ottimizzare statistiche e filtri
- [ ] Misurare miglioramenti performance

### Settimana 4: Re-renders (MEDIO)
- [ ] Spostare oggetti/array inline
- [ ] Implementare useCallback dove necessario
- [ ] Profilare con React DevTools

### Settimana 5+: Refactoring (BASSO)
- [ ] Dividere componenti grandi
- [ ] Estrarre custom hooks
- [ ] Migliorare architettura

---

## 📊 MONITORAGGIO RISULTATI

### Metriche da Tracciare:
1. **Bundle Size:** Riduzione dimensioni bundle
2. **First Paint:** Tempo primo rendering
3. **Interaction Time:** Tempo risposta input
4. **Memory Usage:** Utilizzo memoria
5. **Re-render Count:** Numero re-render

### Strumenti:
- React DevTools Profiler
- Chrome DevTools Performance
- Lighthouse CI
- Bundle Analyzer

---

**Guida creata dai crash test del 28 Giugno 2025**
