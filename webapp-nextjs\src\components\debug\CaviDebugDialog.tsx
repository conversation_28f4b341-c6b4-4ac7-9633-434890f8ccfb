'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Bug, Cable, Database } from 'lucide-react'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { Cavo, ParcoCavo } from '@/types'
import { isCableInstalled, isCableSpare, isCompatible } from '@/utils/bobineUtils'

interface CaviDebugDialogProps {
  open: boolean
  console.log({
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
}

export default function CaviDebugDialog({
  open,
  onClose,
  bobina
}: CaviDebugDialogProps) {
  const { cantiere } = useAuth()
  const [loading, setLoading] = useState(false)
  const [debugData, setDebugData] = useState<{
    totalCavi: number
    console.log({
    totalCavi: number
    caviData: Cavo[]
    filteredCavi: any[]
    compatibleCavi: any[]
    incompatibleCavi: any[]
    filterReasons: Record<string, string>
  } | null>(null)

  useEffect(() => {
    if (open && cantiere && bobina) {
      loadDebugData()
    }
  }, [open, cantiere, bobina])

  const loadDebugData = async () => {
    if (!cantiere || !bobina) return

    try {
      setLoading(true)
      const caviData = await caviApi.getCavi(cantiere.id_cantiere)

      const filterReasons: Record<string, string> = {}
      const filteredCavi: any[] = []
      const compatibleCavi: any[] = []
      const incompatibleCavi: any[] = []

      caviData.forEach(cavo => {
        const metriReali = parseFloat(cavo.metratura_reale?.toString() || '0') || 0
        const metriTeorici = parseFloat(cavo.metri_teorici?.toString() || '0') || 0
        const currentBobina = cavo.id_bobina

        // Usa le stesse utility functions del componente principale
        const isNotInstalled = !isCableInstalled(cavo)
        const isNotSpare = !isCableSpare(cavo)
        const hasNoRealMeters = metriReali === 0
        const hasTheoreticalMeters = metriTeorici > 0
        const canBeReassigned = !currentBobina ||
                               currentBobina === 'BOBINA_VUOTA' ||
                               currentBobina === 'TBD' ||
                               currentBobina !== bobina.id_bobina

        const isAvailable = isNotInstalled && isNotSpare && hasNoRealMeters && hasTheoreticalMeters && canBeReassigned

        if (!isAvailable) {
          filterReasons[cavo.id_cavo] = !isNotInstalled ? 'Già installato' :
                                       !isNotSpare ? 'È SPARE' :
                                       !hasNoRealMeters ? 'Ha metri reali > 0' :
                                       !hasTheoreticalMeters ? 'Metri teorici = 0' :
                                       !canBeReassigned ? 'Già associato a questa bobina' : 'Altro'
        } else {
          filteredCavi.push({
            ...cavo,
            console.log({
            ...cavo,
            debug: {
              metriReali,
              metriTeorici,
              currentBobina,
              isNotInstalled,
              isNotSpare,
              hasNoRealMeters,
              hasTheoreticalMeters,
              canBeReassigned
            }
          })

          // Usa la utility function per compatibilità
          const isCompatibleResult = isCompatible(cavo, bobina)

          const cavoWithCompatibility = {
            ...cavo,
            console.log({
            ...cavo,
            debug: {
              metriReali,
              metriTeorici,
              currentBobina,
              console.log({
              currentBobina,
              cavoTipologia: cavo.tipologia,
              bobinaTipologia: bobina.tipologia,
              cavoFormazione: cavo.sezione,
              bobinaFormazione: bobina.sezione,
              tipologiaMatch: cavo.tipologia === bobina.tipologia,
              formazioneMatch: String(cavo.sezione) === String(bobina.sezione),
              isCompatible: isCompatibleResult
            }
          }

          if (isCompatibleResult) {
            compatibleCavi.push(cavoWithCompatibility)
          } else {
            incompatibleCavi.push(cavoWithCompatibility)
          }
        }
      })

      setDebugData({
        totalCavi: caviData.length,
        caviData,
        filteredCavi,
        compatibleCavi,
        incompatibleCavi,
        filterReasons
      })
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  if (!bobina || !debugData) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            console.log({
            <Bug className="h-5 w-5" />
            Debug: Cavi per bobina {bobina.id_bobina}
          </DialogTitle>
          <DialogDescription>
            Analisi dettagliata del filtro cavi per diagnosticare problemi
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Statistiche generali */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Statistiche Generali</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-5 gap-4 text-sm">
                <div>
                  <div className="font-medium">Totale Cavi</div>
                  <div className="text-2xl font-bold">{debugData.totalCavi}</div>
                </div>
                <div>
                  <div className="font-medium">Disponibili</div>
                  <div className="text-2xl font-bold text-blue-600">{debugData.filteredCavi.length}</div>
                </div>
                <div>
                  <div className="font-medium">Compatibili</div>
                  <div className="text-2xl font-bold text-green-600">{debugData.compatibleCavi.length}</div>
                </div>
                <div>
                  <div className="font-medium">Incompatibili</div>
                  <div className="text-2xl font-bold text-yellow-600">{debugData.incompatibleCavi.length}</div>
                </div>
                <div>
                  <div className="font-medium">Esclusi</div>
                  <div className="text-2xl font-bold text-red-600">
                    {debugData.totalCavi - debugData.filteredCavi.length}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Dettagli bobina */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Dettagli Bobina Target</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="font-medium">ID</div>
                  <div>{bobina.id_bobina}</div>
                </div>
                <div>
                  <div className="font-medium">Tipologia</div>
                  <div>{bobina.tipologia}</div>
                </div>
                <div>
                  <div className="font-medium">Formazione</div>
                  <div>{bobina.sezione}</div>
                </div>
                <div>
                  <div className="font-medium">Metri Residui</div>
                  <div>{bobina.metri_residui}m</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Tabs defaultValue="excluded" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="excluded">
                Esclusi ({Object.keys(debugData.filterReasons).length})
              </TabsTrigger>
              <TabsTrigger value="available">
                Disponibili ({debugData.filteredCavi.length})
              </TabsTrigger>
              <TabsTrigger value="compatible">
                Compatibili ({debugData.compatibleCavi.length})
              </TabsTrigger>
              <TabsTrigger value="incompatible">
                Incompatibili ({debugData.incompatibleCavi.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="excluded" className="space-y-2">
              <div className="text-sm text-gray-600 mb-2">
                Cavi esclusi dal filtro iniziale
              </div>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {Object.entries(debugData.filterReasons).map(([cavoId, reason]) => {
                  const cavo = debugData.caviData.find(c => c.id_cavo === cavoId)
                  return (
                    <Card key={cavoId} className="p-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <span className="font-medium">{cavoId}</span>
                          <div className="text-xs text-gray-500">
                            {cavo?.tipologia} - {cavo?.formazione || cavo?.sezione}
                          </div>
                        </div>
                        <Badge variant="destructive" className="text-xs">
                          {reason}
                        </Badge>
                      </div>
                    </Card>
                  )
                })}
              </div>
            </TabsContent>

            <TabsContent value="available" className="space-y-2">
              <div className="text-sm text-gray-600 mb-2">
                Cavi che hanno superato il filtro iniziale
              </div>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {debugData.filteredCavi.map(cavo => (
                  <Card key={cavo.id_cavo} className="p-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-medium">{cavo.id_cavo}</span>
                        <div className="text-xs text-gray-500">
                          {cavo.tipologia} - {cavo.formazione || cavo.sezione} - {cavo.metri_teorici}m
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        Disponibile
                      </Badge>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="compatible" className="space-y-2">
              <div className="text-sm text-gray-600 mb-2">
                Cavi compatibili con la bobina
              </div>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {debugData.compatibleCavi.map(cavo => (
                  <Card key={cavo.id_cavo} className="p-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-medium">{cavo.id_cavo}</span>
                        <div className="text-xs text-gray-500">
                          {cavo.tipologia} - {cavo.formazione || cavo.sezione} - {cavo.metri_teorici}m
                        </div>
                      </div>
                      <Badge variant="default" className="text-xs">
                        Compatibile
                      </Badge>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="incompatible" className="space-y-2">
              <div className="text-sm text-gray-600 mb-2">
                Cavi incompatibili con la bobina
              </div>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {debugData.incompatibleCavi.map(cavo => (
                  <Card key={cavo.id_cavo} className="p-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-medium">{cavo.id_cavo}</span>
                        <div className="text-xs text-gray-500">
                          {cavo.tipologia} - {cavo.formazione || cavo.sezione} - {cavo.metri_teorici}m
                        </div>
                        <div className="text-xs text-red-500">
                          console.log({
                        <div className="text-xs text-red-500">
                          Tip: {cavo.debug.tipologiaMatch ? '✓' : '✗'} ({cavo.debug.cavoTipologia} vs {cavo.debug.bobinaTipologia}) |
                          Form: {cavo.debug.formazioneMatch ? '✓' : '✗'} ({cavo.debug.cavoFormazione} vs {cavo.debug.bobinaFormazione})
                        </div>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        Incompatibile
                      </Badge>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="flex justify-end">
          <Button onClick={onClose}>Chiudi</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
