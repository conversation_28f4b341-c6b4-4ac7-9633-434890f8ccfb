{"version": 3, "file": "ExecutionContext.js", "sourceRoot": "", "sources": ["../../../../src/cdp/ExecutionContext.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,OAAO,EAAC,eAAe,EAAkB,MAAM,sBAAsB,CAAC;AAGtE,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AACvD,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAC7C,OAAO,EAAC,cAAc,EAAC,MAAM,6BAA6B,CAAC;AAE3D,OAAO,EACL,YAAY,EACZ,gBAAgB,EAChB,UAAU,EACV,gCAAgC,EAChC,mBAAmB,EACnB,QAAQ,GACT,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAC,iBAAiB,EAAC,MAAM,8BAA8B,CAAC;AAC/D,OAAO,EAAC,eAAe,EAAE,aAAa,EAAC,MAAM,uBAAuB,CAAC;AACrE,OAAO,EAAC,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAAC,KAAK,EAAC,MAAM,kBAAkB,CAAC;AAEvC,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AACrC,OAAO,EAAC,gBAAgB,EAAC,MAAM,oBAAoB,CAAC;AAEpD,OAAO,EAAC,WAAW,EAAC,MAAM,eAAe,CAAC;AAC1C,OAAO,EACL,cAAc,EACd,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,GACtB,MAAM,YAAY,CAAC;AAEpB,MAAM,wBAAwB,GAAG,IAAI,OAAO,CAC1C,qBAAqB,EACrB,gBAAgB,CAAC,QAA2C,EAC5D,EAAE,CACH,CAAC;AAEF,MAAM,2BAA2B,GAAG,IAAI,OAAO,CAC7C,wBAAwB,EACxB,CAAC,KAAK,EACJ,OAA4B,EAC5B,QAAgB,EACW,EAAE;IAC7B,MAAM,OAAO,GAAG,gBAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC7D,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,CACvC,CAAC,GAAG,QAAQ,EAAE,EAAE;QACd,OAAO,QAAQ,CAAC;IAClB,CAAC,EACD,GAAG,CAAC,MAAM,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAC9C,CAAC;AACJ,CAAC,CAAoC,EACrC,EAAE,CACH,CAAC;AAEF;;GAEG;AACH,MAAM,OAAO,gBACX,SAAQ,YAMN;IAGF,OAAO,CAAa;IACpB,MAAM,CAAgB;IACtB,GAAG,CAAS;IACZ,KAAK,CAAU;IAEN,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;IAE9C,YACE,MAAkB,EAClB,cAA4D,EAC5D,KAAoB;QAEpB,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,EAAE,CAAC;QAC7B,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC;QACnC,CAAC;QACD,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC5E,aAAa,CAAC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,aAAa,CAAC,EAAE,CAAC,mCAAmC,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAClE,IAAI,KAAK,CAAC,kBAAkB,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC1C,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;YACxB,CAAC;QACH,CAAC,CAAC,CAAC;QACH,aAAa,CAAC,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAC9D,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;QACH,aAAa,CAAC,EAAE,CAAC,0BAA0B,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,aAAa,CAAC,EAAE,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAClD,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,+EAA+E;IAC/E,SAAS,GAAG,IAAI,GAAG,EAAmB,CAAC;IAEvC,yEAAyE;IACzE,yEAAyE;IACzE,MAAM,GAAG,IAAI,KAAK,EAAE,CAAC;IACrB,KAAK,CAAC,WAAW,CAAC,OAAgB;;;YAChC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,OAAO;YACT,CAAC;YAED,MAAM,CAAC,kCAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,QAAA,CAAC;YACtC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CACrB,oBAAoB,EACpB,IAAI,CAAC,KAAK;oBACR,CAAC,CAAC;wBACE,IAAI,EAAE,kBAAkB,GAAG,OAAO,CAAC,IAAI;wBACvC,oBAAoB,EAAE,IAAI,CAAC,KAAK;qBACjC;oBACH,CAAC,CAAC;wBACE,IAAI,EAAE,kBAAkB,GAAG,OAAO,CAAC,IAAI;wBACvC,kBAAkB,EAAE,IAAI,CAAC,GAAG;qBAC7B,CACN,CAAC;gBAEF,MAAM,IAAI,CAAC,QAAQ,CACjB,cAAc,EACd,UAAU,EACV,OAAO,CAAC,IAAI,EACZ,kBAAkB,CACnB,CAAC;gBAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC5C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,iEAAiE;gBACjE,uEAAuE;gBACvE,mCAAmC;gBACnC,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;oBAC3B,qBAAqB;oBACrB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iCAAiC,CAAC,EAAE,CAAC;wBAC9D,OAAO;oBACT,CAAC;oBACD,mBAAmB;oBACnB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC,EAAE,CAAC;wBACpE,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,UAAU,CAAC,KAAK,CAAC,CAAC;YACpB,CAAC;;;;;;;;;KACF;IAED,KAAK,CAAC,gBAAgB,CACpB,KAA0C;QAE1C,IAAI,KAAK,CAAC,kBAAkB,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;YAC1C,OAAO;QACT,CAAC;QAED,IAAI,OAAuB,CAAC;QAC5B,IAAI,CAAC;YACH,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAAC,MAAM,CAAC;YACP,mEAAmE;YACnE,6CAA6C;YAC7C,OAAO;QACT,CAAC;QACD,MAAM,EAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAC,GAAG,OAAO,CAAC;QACnD,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YACxB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAClC,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YAClC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzC,MAAM,OAAO,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;QACjD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,UAAU,CAAC,GAAG,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB,CAAC;IAED,aAAa,CAAC,KAA6C;QACzD,IAAI,KAAK,CAAC,kBAAkB,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;YAC1C,OAAO;QACT,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IACvC,CAAC;IAED,kBAAkB,GAAG,KAAK,CAAC;IAC3B,cAAc,CAAoC;IAClD,IAAI,aAAa;QACf,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,EAAsB,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,0BAA0B,CAAC,wBAAwB,CAAC;gBACzD,IAAI,CAAC,0BAA0B,CAAC,2BAA2B,CAAC;aAC7D,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACrC,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBACtC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAqC,CAAC;YACzE,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,cAAkD,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,OAAgB;QAC/C,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,0EAA0E;YAC1E,uEAAuE;YACvE,gCAAgC;YAChC,UAAU,CAAC,GAAG,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACH,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgDG;IACH,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC5D,CAAC;IAkBD,KAAK,CAAC,SAAS,CAIb,aAAsB,EACtB,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,gBAAgB,GAAG,mBAAmB,CAC1C,gCAAgC,CAAC,YAAY,CAAC,EAAE,QAAQ,EAAE;YACxD,YAAY,CAAC,YAAY,CAC5B,CAAC;QAEF,IAAI,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC;YAC3B,MAAM,UAAU,GAAG,YAAY,CAAC;YAChC,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC/D,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,GAAG,UAAU,KAAK,gBAAgB,IAAI,CAAC;YAE3C,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO;iBAChE,IAAI,CAAC,kBAAkB,EAAE;gBACxB,UAAU,EAAE,uBAAuB;gBACnC,SAAS;gBACT,aAAa;gBACb,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,IAAI;aAClB,CAAC;iBACD,KAAK,CAAC,YAAY,CAAC,CAAC;YAEvB,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,OAAO,aAAa;gBAClB,CAAC,CAAC,qBAAqB,CAAC,YAAY,CAAC;gBACrC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAChD,CAAC;QAED,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAC5D,MAAM,gCAAgC,GAAG,gBAAgB,CAAC,IAAI,CAC5D,mBAAmB,CACpB;YACC,CAAC,CAAC,mBAAmB;YACrB,CAAC,CAAC,GAAG,mBAAmB,KAAK,gBAAgB,IAAI,CAAC;QACpD,IAAI,qBAAqB,CAAC;QAC1B,IAAI,CAAC;YACH,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAClE,mBAAmB,EAAE,gCAAgC;gBACrD,kBAAkB,EAAE,IAAI,CAAC,GAAG;gBAC5B,oEAAoE;gBACpE,sCAAsC;gBACtC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;oBACzB,OAAO,GAAG,YAAY,OAAO,CAAC;gBAChC,CAAC,CAAC;oBACA,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBACb,OAAO,oBAAoB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;oBACzC,CAAC,CAAC,CACH;oBACH,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;wBACb,OAAO,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;oBACpC,CAAC,CAAC;gBACN,aAAa;gBACb,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IACE,KAAK,YAAY,SAAS;gBAC1B,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,uCAAuC,CAAC,EACjE,CAAC;gBACD,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC;YACzD,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;QACD,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAC5C,MAAM,qBAAqB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,aAAa;YAClB,CAAC,CAAC,qBAAqB,CAAC,YAAY,CAAC;YACrC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAE9C,KAAK,UAAU,oBAAoB,CACjC,OAAyB,EACzB,GAAY;YAEZ,IAAI,GAAG,YAAY,OAAO,EAAE,CAAC;gBAC3B,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC/B,CAAC;YACD,OAAO,eAAe,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACvC,CAAC;QAED,SAAS,eAAe,CACtB,OAAyB,EACzB,GAAY;YAEZ,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;gBAC5B,OAAO,EAAC,mBAAmB,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAC,CAAC;YACrD,CAAC;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,OAAO,EAAC,mBAAmB,EAAE,IAAI,EAAC,CAAC;YACrC,CAAC;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO,EAAC,mBAAmB,EAAE,UAAU,EAAC,CAAC;YAC3C,CAAC;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9B,OAAO,EAAC,mBAAmB,EAAE,WAAW,EAAC,CAAC;YAC5C,CAAC;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;gBACxB,OAAO,EAAC,mBAAmB,EAAE,KAAK,EAAC,CAAC;YACtC,CAAC;YACD,MAAM,YAAY,GAChB,GAAG,IAAI,CAAC,GAAG,YAAY,WAAW,IAAI,GAAG,YAAY,gBAAgB,CAAC;gBACpE,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,IAAI,CAAC;YACX,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,YAAY,CAAC,KAAK,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;oBAC1C,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;gBACJ,CAAC;gBACD,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;oBAC1B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBAC3C,CAAC;gBACD,IAAI,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE,CAAC;oBACpD,OAAO;wBACL,mBAAmB,EACjB,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB;qBAClD,CAAC;gBACJ,CAAC;gBACD,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC1C,OAAO,EAAC,KAAK,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,KAAK,EAAC,CAAC;gBACpD,CAAC;gBACD,OAAO,EAAC,QAAQ,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAC,CAAC;YAC1D,CAAC;YACD,OAAO,EAAC,KAAK,EAAE,GAAG,EAAC,CAAC;QACtB,CAAC;IACH,CAAC;IAEQ,CAAC,aAAa,CAAC;QACtB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;QAC5B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IACnC,CAAC;CACF;AAED,MAAM,YAAY,GAAG,CAAC,KAAY,EAAqC,EAAE;IACvE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oCAAoC,CAAC,EAAE,CAAC;QACjE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;IACvC,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAAE,CAAC;QACnE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;IACvC,CAAC;IAED,IACE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC;QAC/D,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAC9D,CAAC;QACD,MAAM,IAAI,KAAK,CACb,uEAAuE,CACxE,CAAC;IACJ,CAAC;IACD,MAAM,KAAK,CAAC;AACd,CAAC,CAAC"}