{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "f58a3ce34f68fc8bad99c122d2bffac7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "37b0cd738cccee8a60c11630a351ea72664316b7c7f50bde60942dea3afd5580", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7ad3d2915270e3678ffa4a4047fb3555a39631f992e56dc9b219940fcddab1eb"}}}, "sortedMiddleware": ["/"], "functions": {}}