# Fix Timeout Error - Reports Page

## 🐛 Problema Identificato
La pagina Reports stava riscontrando errori di timeout durante il caricamento delle API, causando il seguente errore:

```
Error: Timeout
Call Stack: ReportsPage.useEffect.loadAllReports.fetchWithTimeout
```

## 🔧 Soluzioni Implementate

### 1. Aumento Timeout API
**Prima**: 10 secondi (10000ms)
**Dopo**: 30 secondi (30000ms)

```typescript
// Helper function per timeout - aumentato timeout per API lente
const fetchWithTimeout = (url: string, options: any, timeout = 30000) => {
  return Promise.race([
    fetch(url, options),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error(`Timeout dopo ${timeout/1000}s per ${url}`)), timeout)
    )
  ]) as Promise<Response>
}
```

### 2. Miglioramento Error Handling
Aggiunto error handling specifico per timeout con messaggi più informativi:

```typescript
.catch(err => {
  console.error('❌ Error loading progress report:', err)
  return { content: null, error: err.message.includes('Timeout') ? 'Timeout API Progress' : 'Errore API Progress' }
})
```

### 3. Gestione Timeout Parziali
Implementata logica per gestire timeout parziali - se alcune API funzionano e altre vanno in timeout:

```typescript
// Check for timeout errors specifically
const hasTimeoutErrors = [progressData, boqData, utilizzoData].some(data => 
  data?.error?.includes('Timeout')
)

if (hasTimeoutErrors) {
  setError('Alcuni report hanno riscontrato timeout. I dati disponibili sono mostrati sotto.')
} else {
  setError('')
}
```

### 4. UI Migliorata per Timeout
Aggiunto feedback specifico per errori di timeout con suggerimenti utili:

```typescript
{error.includes('timeout') || error.includes('Timeout') ? (
  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
    <p className="text-blue-800 text-sm">
      💡 <strong>Suggerimento:</strong> Le API stanno impiegando più tempo del previsto. 
      Prova ad aggiornare la pagina o riprova tra qualche minuto.
    </p>
  </div>
) : null}
```

### 5. Refresh Migliorato
Migliorata la funzione di refresh per pulire completamente lo stato:

```typescript
const handleRefresh = () => {
  if (cantiere?.id_cantiere) {
    setIsLoading(true)
    setError('')
    setReportProgress(null)
    setReportBOQ(null)
    setReportUtilizzoBobine(null)
    // Force re-run of useEffect
    setTimeout(() => {
      window.location.reload()
    }, 100)
  }
}
```

## 📊 API Endpoints Coinvolti

Le seguenti API sono state ottimizzate per gestire timeout:

1. **Progress API**: `/api/reports/${cantiereId}/progress?formato=video`
2. **BOQ API**: `/api/reports/${cantiereId}/boq?formato=video`
3. **Utilizzo Bobine API**: `/api/reports/${cantiereId}/storico-bobine?formato=video`

## 🎯 Risultati

### ✅ Miglioramenti Implementati
- **Timeout aumentato**: Da 10s a 30s per API lente
- **Error handling specifico**: Messaggi di errore più informativi
- **Gestione timeout parziali**: Mostra dati disponibili anche se alcune API falliscono
- **UI migliorata**: Feedback utente più chiaro e suggerimenti utili
- **Refresh ottimizzato**: Pulizia completa dello stato prima del reload

### 🔄 Comportamento Atteso
1. **API veloci**: Caricamento normale entro 30s
2. **API lente**: Timeout dopo 30s con messaggio specifico
3. **Timeout parziali**: Mostra dati disponibili + avviso timeout
4. **Retry**: Pulsante refresh con pulizia stato completa

### 📈 Performance
- **Timeout più realistico**: 30s invece di 10s per API complesse
- **Graceful degradation**: Funzionalità parziale invece di fallimento totale
- **User experience**: Feedback chiaro e opzioni di recovery

## 🚀 Test Consigliati

1. **Test Timeout**: Simulare API lente per verificare timeout
2. **Test Parziali**: Verificare comportamento con alcune API funzionanti
3. **Test Refresh**: Verificare pulizia stato e reload
4. **Test UI**: Verificare messaggi di errore e suggerimenti

## 📝 Note Tecniche

### Pattern Utilizzati
- **Promise.race()**: Per implementare timeout custom
- **Error categorization**: Distinguere timeout da altri errori
- **Graceful degradation**: Mostrare dati parziali quando possibile
- **User feedback**: Messaggi informativi e azioni suggerite

### Considerazioni Future
- **Retry automatico**: Implementare retry automatico per timeout
- **Caching**: Cache dei dati per ridurre chiamate API
- **Loading progressivo**: Mostrare dati man mano che arrivano
- **Background refresh**: Aggiornamento dati in background

---

**Fix implementato con successo** ✅  
**Data**: 28 Giugno 2025  
**Timeout aumentato**: 10s → 30s  
**Error handling**: Migliorato  
**User experience**: Ottimizzata
