'use client'

import { useState, useRef } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  DialogTitle} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Upload, FileSpreadsheet, CheckCircle } from 'lucide-react'
import { excelApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface ImportExcelDialogProps {
  open: boolean
  }

export default function ImportExcelDialog({
  open,
  onClose,
  tipo,
  onSuccess,
  onError
}: ImportExcelDialogProps) {
  const { cantiere } = useAuth()
  const [file, setFile] = useState<File | null>(null)
  const [revisione, setRevisione] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [uploadProgress, setUploadProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (selectedFile) {
      // Verifica che sia un file Excel
      const validTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel'
      ]
      
      if (!validTypes.includes(selectedFile.type) && 
          !selectedFile.name.toLowerCase().endsWith('.xlsx') && 
          !selectedFile.name.toLowerCase().endsWith('.xls')) {
        setError('Seleziona un file Excel valido (.xlsx o .xls)')
        return
      }

      setFile(selectedFile)
      setError('')
    }
  }

  const handleImport = async () => {
    if (!file || !cantiere) return

    if (tipo === 'cavi' && !revisione.trim()) {
      setError('Inserisci il codice revisione per l\'importazione cavi')
      return
    }

    try {
      setLoading(true)
      setError('')
      setUploadProgress(0)

      let response
      if (tipo === 'cavi') {
        response = await excelApi.importCavi(cantiere.id_cantiere, file, revisione.trim())
      } else {
        response = await excelApi.importBobine(cantiere.id_cantiere, file)
      }

      setUploadProgress(100)

      if (response.data.success) {
        const details = response.data.details
        let message = response.data.message
        
        if (tipo === 'cavi' && details?.cavi_importati) {
          message += ` (${details.cavi_importati} cavi importati)`
        } else if (tipo === 'bobine' && details?.bobine_importate) {
          message += ` (${details.bobine_importate} bobine importate)`
        }

        onSuccess(message)
        onClose()
      } else {
        onError(response.data.message || 'Errore durante l\'importazione')
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\'importazione del file'
      onError(errorMessage)
    } finally {
      setLoading(false)
      setUploadProgress(0)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setFile(null)
      setRevisione('')
      setError('')
      setUploadProgress(0)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
      onClose()
    }
  }

  const getTipoLabel = () => {
    return tipo === 'cavi' ? 'Cavi' : 'Bobine'
  }

  const getFileRequirements = () => {
    if (tipo === 'cavi') {
      return [
        'File Excel (.xlsx o .xls)',
        'Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.',
        'Prima riga deve contenere le intestazioni',
        'Codice revisione obbligatorio per tracciabilità'
      ]
    } else {
      return [
        'File Excel (.xlsx o .xls)',
        'Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.',
        'Prima riga deve contenere le intestazioni',
        'I metri residui saranno impostati uguali ai metri totali'
      ]
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Importa {getTipoLabel()} da Excel
          </DialogTitle>
          <DialogDescription>
            Carica un file Excel per importare {getTipoLabel().toLowerCase()} nel cantiere
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Requisiti file */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <Label className="text-sm font-medium">Requisiti File</Label>
            <ul className="mt-2 space-y-1 text-sm text-gray-600">
              {getFileRequirements().map((req, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>{req}</span>
                </li>
              ))}
            </ul>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Selezione file */}
          <div className="space-y-2">
            <Label htmlFor="file">File Excel *</Label>
            <div className="flex items-center gap-2">
              <Input
                ref={fileInputRef}
                id="file"
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileSelect}
                disabled={loading}
                className="flex-1"
              />
              {file && (
                <div className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">File selezionato</span>
                </div>
              )}
            </div>
            {file && (
              <div className="text-sm text-gray-600">
                <FileSpreadsheet className="h-4 w-4 inline mr-1" />
                {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
              </div>
            )}
          </div>

          {/* Revisione (solo per cavi) */}
          {tipo === 'cavi' && (
            <div className="space-y-2">
              <Label htmlFor="revisione">Codice Revisione *</Label>
              <Input
                id="revisione"
                value={revisione}
                onChange={(e) => setRevisione(e.target.value)}
                placeholder="es. REV001, V1.0, 2024-01"
                disabled={loading}
              />
              <p className="text-sm text-gray-500">
                Codice identificativo della revisione per tracciabilità delle modifiche
              </p>
            </div>
          )}

          {/* Progress bar */}
          {loading && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">Caricamento in corso...</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Riepilogo */}
          {file && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <Label className="text-sm font-medium">Riepilogo Importazione</Label>
              <div className="mt-2 space-y-1 text-sm">
                <div><strong>Tipo:</strong> {getTipoLabel()}</div>
                <div><strong>File:</strong> {file.name}</div>
                <div><strong>Dimensione:</strong> {(file.size / 1024 / 1024).toFixed(2)} MB</div>
                {tipo === 'cavi' && revisione && (
                  <div><strong>Revisione:</strong> {revisione}</div>
                )}
                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Annulla
          </Button>
          <Button
            onClick={handleImport}
            disabled={loading || !file || (tipo === 'cavi' && !revisione.trim())}
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Upload className="h-4 w-4 mr-2" />}
            Importa {getTipoLabel()}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
