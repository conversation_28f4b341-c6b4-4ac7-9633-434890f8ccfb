'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Award, FileText, Download } from 'lucide-react'
import { Cavo } from '@/types'
import { certificazioniApi, responsabiliApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface CertificazioneDialogProps {
  open: boolean
  console.log({
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface Responsabile {
  id: number
  console.log({
  id: number
  nome_responsabile: string
  numero_telefono?: string
  mail?: string
}

export default function CertificazioneDialog({
  open,
  onClose,
  cavo,
  onSuccess,
  onError
}: CertificazioneDialogProps) {
  const { cantiere } = useAuth()
  const [formData, setFormData] = useState({
    responsabile_certificazione: '',
    console.log({
    responsabile_certificazione: '',
    data_certificazione: new Date().toISOString().split('T')[0],
    esito_certificazione: 'CONFORME',
    note_certificazione: ''
  })
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingResponsabili, setLoadingResponsabili] = useState(false)
  const [error, setError] = useState('')

  // Reset form quando si apre il dialog
  useEffect(() => {
    if (open && cavo) {
      setFormData({
        responsabile_certificazione: '',
        console.log({
        responsabile_certificazione: '',
        data_certificazione: new Date().toISOString().split('T')[0],
        esito_certificazione: 'CONFORME',
        note_certificazione: ''
      })
      setError('')
      loadResponsabili()
    }
  }, [open, cavo])

  const loadResponsabili = async () => {
    if (!cantiere) return

    try {
      setLoadingResponsabili(true)
      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)
      setResponsabili(response.data)
    } catch (error) {
      setResponsabili([])
    } finally {
      setLoadingResponsabili(false)
    }
  }

  const isCavoCollegato = () => {
    if (!cavo) return false
    const collegamento = cavo.collegamento || cavo.collegamenti || 0
    return collegamento === 3 // Completamente collegato
  }

  const isCavoCertificato = () => {
    if (!cavo) return false
    return cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'
  }

  const handleCertifica = async () => {
    if (!cavo || !cantiere) return

    if (!formData.responsabile_certificazione) {
      setError('Seleziona un responsabile per la certificazione')
      return
    }

    try {
      setLoading(true)
      setError('')

      const certificazioneData = {
        id_cavo: cavo.id_cavo,
        console.log({
        id_cavo: cavo.id_cavo,
        responsabile_certificazione: formData.responsabile_certificazione,
        data_certificazione: formData.data_certificazione,
        esito_certificazione: formData.esito_certificazione,
        note_certificazione: formData.note_certificazione || null
      }

      await certificazioniApi.createCertificazione(cantiere.id_cantiere, certificazioneData)

      onSuccess(`Certificazione completata per il cavo ${cavo.id_cavo}`)
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la certificazione'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleGeneraPDF = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')

      const response = await certificazioniApi.generatePDF(cantiere.id_cantiere, cavo.id_cavo)
      
      // Crea un link per il download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `certificato_${cavo.id_cavo}.pdf`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      onSuccess(`PDF certificato generato per il cavo ${cavo.id_cavo}`)
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la generazione del PDF'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  if (!cavo) return null

  const isInstalled = (cavo.metri_posati || cavo.metratura_reale || 0) > 0
  const isCollegato = isCavoCollegato()
  const isCertificato = isCavoCertificato()

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Gestione Certificazione - {cavo.id_cavo}
          </DialogTitle>
          <DialogDescription>
            Certifica il cavo {cavo.id_cavo} o genera il PDF del certificato
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Stato attuale */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <Label className="text-sm font-medium">Stato Cavo</Label>
            <div className="mt-2 space-y-1">
              <div className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${isInstalled ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span className="text-sm">{isInstalled ? 'Installato' : 'Non installato'}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${isCollegato ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span className="text-sm">{isCollegato ? 'Collegato' : 'Non collegato'}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className={`w-3 h-3 rounded-full ${isCertificato ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span className="text-sm">{isCertificato ? 'Certificato' : 'Non certificato'}</span>
              </div>
            </div>
          </div>

          {!isInstalled && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Il cavo deve essere installato prima di poter essere certificato.
              </AlertDescription>
            </Alert>
          )}

          {!isCollegato && isInstalled && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Il cavo deve essere completamente collegato prima di poter essere certificato.
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isCertificato ? (
            // Cavo già certificato - mostra opzione per generare PDF
            <div className="space-y-4">
              <Alert>
                <Award className="h-4 w-4" />
                <AlertDescription>
                  Questo cavo è già stato certificato. Puoi generare il PDF del certificato.
                </AlertDescription>
              </Alert>

              <Button
                onClick={handleGeneraPDF}
                disabled={loading}
                className="w-full"
              >
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Download className="h-4 w-4 mr-2" />}
                Genera PDF Certificato
              </Button>
            </div>
          ) : (
            // Cavo non certificato - mostra form per certificazione
            isInstalled && isCollegato && (
              <div className="space-y-4">
                {/* Responsabile */}
                <div className="space-y-2">
                  <Label htmlFor="responsabile">Responsabile Certificazione *</Label>
                  <Select
                    value={formData.responsabile_certificazione}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile_certificazione: value }))}
                    disabled={loadingResponsabili}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleziona responsabile..." />
                    </SelectTrigger>
                    <SelectContent>
                      {responsabili.map((resp) => (
                        <SelectItem key={resp.id} value={resp.nome_responsabile}>
                          {resp.nome_responsabile}
                          {resp.numero_telefono && ` - ${resp.numero_telefono}`}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Data certificazione */}
                <div className="space-y-2">
                  <Label htmlFor="data">Data Certificazione</Label>
                  <Input
                    id="data"
                    type="date"
                    value={formData.data_certificazione}
                    onChange={(e) => setFormData(prev => ({ ...prev, data_certificazione: e.target.value }))}
                  />
                </div>

                {/* Esito */}
                <div className="space-y-2">
                  <Label htmlFor="esito">Esito Certificazione</Label>
                  <Select
                    value={formData.esito_certificazione}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, esito_certificazione: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="CONFORME">CONFORME</SelectItem>
                      <SelectItem value="NON_CONFORME">NON CONFORME</SelectItem>
                      <SelectItem value="CONFORME_CON_RISERVA">CONFORME CON RISERVA</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Note */}
                <div className="space-y-2">
                  <Label htmlFor="note">Note (opzionale)</Label>
                  <Textarea
                    id="note"
                    placeholder="Inserisci eventuali note sulla certificazione..."
                    value={formData.note_certificazione}
                    onChange={(e) => setFormData(prev => ({ ...prev, note_certificazione: e.target.value }))}
                    rows={3}
                  />
                </div>

                <Button
                  onClick={handleCertifica}
                  disabled={loading || !formData.responsabile_certificazione}
                  className="w-full"
                >
                  {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Award className="h-4 w-4 mr-2" />}
                  Certifica Cavo
                </Button>
              </div>
            )
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Chiudi
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
