'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Plus } from 'lucide-react'
import { parcoCaviApi } from '@/lib/api'


interface CreaBobinaDialogProps {
  open: boolean
  onClose: () => void
  cantiereId: number
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface FormData {
  numero_bobina: string
  utility: string
  tipologia: string
  n_conduttori: string
  sezione: string
  metri_totali: string
  ubicazione_bobina: string
  fornitore: string
  n_DDT: string
  data_DDT: string
  configurazione: string
}

const initialFormData: FormData = {
  numero_bobina: '',
  utility: '',
  tipologia: '',
  n_conduttori: '0',
  sezione: '',
  metri_totali: '',
  ubicazione_bobina: 'TBD',
  fornitore: 'TBD',
  n_DDT: 'TBD',
  data_DDT: '',
  configurazione: 's'
}

export default function CreaBobinaDialog({
  open,
  onClose,
  cantiereId,
  onSuccess,
  onError
}: CreaBobinaDialogProps) {
  const [formData, setFormData] = useState<FormData>(initialFormData)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [nextBobinaNumber, setNextBobinaNumber] = useState('1')
  const [loadingConfig, setLoadingConfig] = useState(false)
  const [isFirstInsertion, setIsFirstInsertion] = useState(true)
  const [configurazioneFixed, setConfigurazioneFixed] = useState('')
  const [showConfigSelection, setShowConfigSelection] = useState(false)

  // Reset form quando si apre il dialog
  useEffect(() => {
    if (open && cantiereId && cantiereId > 0) {
      setFormData(initialFormData)
      setError('')
      checkFirstInsertion()
    }
  }, [open, cantiereId])

  // Verifica se è il primo inserimento e carica la configurazione
  const checkFirstInsertion = async () => {
    if (!cantiereId || cantiereId <= 0) return

    try {
      setLoadingConfig(true)
      const response = await parcoCaviApi.isFirstBobinaInsertion(cantiereId)

      setIsFirstInsertion(response.is_first_insertion)

      if (response.is_first_insertion) {
        // Primo inserimento: mostra selezione configurazione
        setShowConfigSelection(true)
        setConfigurazioneFixed('')
      } else {
        // Non è il primo inserimento: usa configurazione esistente
        setConfigurazioneFixed(response.configurazione)
        setFormData(prev => ({
          ...prev,
          configurazione: response.configurazione
        }))
        setShowConfigSelection(false)

        // Carica il prossimo numero se configurazione è standard
        if (response.configurazione === 's') {
          await loadNextBobinaNumber()
        }
      }
    } catch (error) {
      console.error('Errore nel controllo primo inserimento:', error)
      // In caso di errore, assume primo inserimento
      setIsFirstInsertion(true)
      setShowConfigSelection(true)
    } finally {
      setLoadingConfig(false)
    }
  }

  // Funzione per caricare il prossimo numero bobina (numerazione globale)
  const loadNextBobinaNumber = async () => {
    if (!cantiereId || cantiereId <= 0) return

    try {
      // Per la numerazione globale, recupera TUTTE le bobine di TUTTI i cantieri
      // e trova il numero massimo globale
      const bobine = await parcoCaviApi.getBobine(cantiereId)

      if (bobine && bobine.length > 0) {
        // Filtra solo le bobine con numero_bobina numerico
        const numericBobine = bobine.filter(b =>
          b.numero_bobina && /^\d+$/.test(b.numero_bobina)
        )

        if (numericBobine.length > 0) {
          // Trova il numero massimo tra le bobine esistenti
          const maxNumber = Math.max(...numericBobine.map(b => parseInt(b.numero_bobina, 10)))
          const nextNumber = String(maxNumber + 1)
          setNextBobinaNumber(nextNumber)

          // Se la configurazione è standard, imposta automaticamente il numero
          setFormData(prev => ({
            ...prev,
            numero_bobina: nextNumber
          }))
        } else {
          setNextBobinaNumber('1')
          setFormData(prev => ({
            ...prev,
            numero_bobina: '1'
          }))
        }
      } else {
        setNextBobinaNumber('1')
        setFormData(prev => ({
          ...prev,
          numero_bobina: '1'
        }))
      }
    } catch (error) {
      console.error('Errore nel recupero del prossimo numero bobina:', error)
      setNextBobinaNumber('1')
      setFormData(prev => ({
        ...prev,
        numero_bobina: '1'
      }))
    }
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    setError('')
  }

  // Gestisce la selezione della configurazione (solo al primo inserimento)
  const handleConfigSelection = async (config: string) => {
    setConfigurazioneFixed(config)
    setFormData(prev => ({
      ...prev,
      configurazione: config
    }))
    setShowConfigSelection(false)

    // Se configurazione standard, carica il prossimo numero
    if (config === 's') {
      await loadNextBobinaNumber()
    } else {
      // Configurazione manuale: svuota il campo
      setFormData(prev => ({
        ...prev,
        numero_bobina: ''
      }))
    }
  }

  const validateForm = (): string | null => {
    if (!formData.numero_bobina.trim()) {
      return 'Il numero bobina è obbligatorio'
    }

    // Validazione per configurazione manuale
    if (formData.configurazione === 'm') {
      const numeroInput = formData.numero_bobina.trim()

      // Verifica caratteri non consentiti
      if (/[\s\\/:*?"<>|]/.test(numeroInput)) {
        return 'Il numero bobina non può contenere spazi o caratteri speciali come \\ / : * ? " < > |'
      }
    }

    if (!formData.utility.trim()) {
      return 'La utility è obbligatoria'
    }
    if (!formData.tipologia.trim()) {
      return 'La tipologia è obbligatoria'
    }
    if (!formData.sezione.trim()) {
      return 'La formazione è obbligatoria'
    }
    if (!formData.metri_totali.trim()) {
      return 'I metri totali sono obbligatori'
    }

    const metri = parseFloat(formData.metri_totali)
    if (isNaN(metri) || metri <= 0) {
      return 'I metri totali devono essere un numero positivo'
    }

    return null
  }

  const handleSave = async () => {
    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      return
    }

    try {
      setLoading(true)
      setError('')

      if (!cantiereId || cantiereId <= 0) {
        throw new Error('Cantiere non selezionato')
      }

      // Prepara i dati per l'API
      const bobinaData = {
        numero_bobina: formData.numero_bobina.trim(),
        utility: formData.utility.trim().toUpperCase(),
        tipologia: formData.tipologia.trim().toUpperCase(),
        n_conduttori: '0', // Campo disponibile, sempre impostato a '0'
        sezione: formData.sezione.trim(),
        metri_totali: parseFloat(formData.metri_totali),
        ubicazione_bobina: formData.ubicazione_bobina.trim() || 'TBD',
        fornitore: formData.fornitore.trim() || 'TBD',
        n_DDT: formData.n_DDT.trim() || 'TBD',
        data_DDT: formData.data_DDT || null,
        configurazione: formData.configurazione
      }

      await parcoCaviApi.createBobina(cantiereId, bobinaData)

      onSuccess(`Bobina ${formData.numero_bobina} creata con successo`)
      onClose()
    } catch (error: any) {
      console.error('Errore nella creazione bobina:', error)
      const errorDetail = error.response?.data?.detail || error.message || 'Errore durante la creazione della bobina'

      // Gestione specifica per ID bobina duplicato
      if (errorDetail.includes('già presente nel cantiere') || errorDetail.includes('già esistente')) {
        setError(`⚠️ Bobina con numero ${formData.numero_bobina} già esistente. Scegli un numero diverso.`)
      } else {
        onError(errorDetail)
      }
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    if (!loading) {
      setFormData(initialFormData)
      setError('')
      onClose()
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent
        className="max-h-[95vh] overflow-y-auto"
        style={{
          width: '1000px !important',
          maxWidth: '95vw !important',
          minWidth: '1000px'
        }}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Crea Nuova Bobina
          </DialogTitle>
          <DialogDescription>
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Caricamento configurazione */}
          {loadingConfig && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              <span>Verifica configurazione...</span>
            </div>
          )}

          {/* Selezione configurazione (solo primo inserimento) */}
          {showConfigSelection && !loadingConfig && (
            <div className="border rounded-lg p-4 bg-blue-50">
              <h4 className="font-medium mb-3">Seleziona configurazione per questo cantiere</h4>
              <p className="text-sm text-gray-600 mb-4">
                Questa scelta determinerà come verranno numerati tutti i futuri inserimenti di bobine in questo cantiere.
              </p>
              <div className="grid grid-cols-1 gap-3">
                <Button
                  variant="outline"
                  className="justify-start h-auto p-4"
                  onClick={() => handleConfigSelection('s')}
                >
                  <div className="text-left">
                    <div className="font-medium">Standard (s) - Numerazione automatica</div>
                    <div className="text-sm text-gray-600">I numeri bobina vengono generati automaticamente: 1, 2, 3...</div>
                  </div>
                </Button>
                <Button
                  variant="outline"
                  className="justify-start h-auto p-4"
                  onClick={() => handleConfigSelection('m')}
                >
                  <div className="text-left">
                    <div className="font-medium">Manuale (m) - Inserimento manuale</div>
                    <div className="text-sm text-gray-600">Puoi inserire numeri personalizzati: A123, TEST01, ecc.</div>
                  </div>
                </Button>
              </div>
            </div>
          )}



          {/* Layout a due colonne per i campi */}
          <div className="grid grid-cols-2 gap-6">
            {/* Colonna sinistra */}
            <div className="space-y-4">
              {/* Numero Bobina */}
              {!showConfigSelection && !loadingConfig && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="numero_bobina" className="text-right">
                    Bobina *
                  </Label>
                  <Input
                    id="numero_bobina"
                    value={formData.numero_bobina}
                    onChange={(e) => handleInputChange('numero_bobina', e.target.value)}
                    placeholder={formData.configurazione === 's' ? 'Generato automaticamente' : 'Es: A123, TEST01'}
                    disabled={loading || formData.configurazione === 's'}
                    className={`col-span-2 ${formData.configurazione === 's' ? 'bg-gray-50' : ''}`}
                  />
                </div>
              )}

              {/* Utility */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="utility" className="text-right">
                  Utility *
                </Label>
                <Input
                  id="utility"
                  value={formData.utility}
                  onChange={(e) => handleInputChange('utility', e.target.value)}
                  className="col-span-2"
                  placeholder="Es: ENEL, TIM, OPEN FIBER"
                  disabled={loading}
                />
              </div>

              {/* Tipologia */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="tipologia" className="text-right">
                  Tipologia *
                </Label>
                <Input
                  id="tipologia"
                  value={formData.tipologia}
                  onChange={(e) => handleInputChange('tipologia', e.target.value)}
                  className="col-span-2"
                  placeholder="Es: FO, RAME"
                  disabled={loading}
                />
              </div>

              {/* Formazione */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="sezione" className="text-right">
                  Formazione *
                </Label>
                <Input
                  id="sezione"
                  value={formData.sezione}
                  onChange={(e) => handleInputChange('sezione', e.target.value)}
                  className="col-span-2"
                  placeholder="Es: 9/125, 50/125, 1.5"
                  disabled={loading}
                />
              </div>

              {/* Metri Totali */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="metri_totali" className="text-right">
                  Metri Totali *
                </Label>
                <Input
                  id="metri_totali"
                  type="number"
                  step="0.1"
                  min="0"
                  value={formData.metri_totali}
                  onChange={(e) => handleInputChange('metri_totali', e.target.value)}
                  className="col-span-2"
                  placeholder="Es: 1000"
                  disabled={loading}
                />
              </div>
            </div>

            {/* Colonna destra */}
            <div className="space-y-4">
              {/* Ubicazione */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="ubicazione_bobina" className="text-right">
                  Ubicazione
                </Label>
                <Input
                  id="ubicazione_bobina"
                  value={formData.ubicazione_bobina}
                  onChange={(e) => handleInputChange('ubicazione_bobina', e.target.value)}
                  className="col-span-2"
                  placeholder="Es: Magazzino A, Cantiere"
                  disabled={loading}
                />
              </div>

              {/* Fornitore */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="fornitore" className="text-right">
                  Fornitore
                </Label>
                <Input
                  id="fornitore"
                  value={formData.fornitore}
                  onChange={(e) => handleInputChange('fornitore', e.target.value)}
                  className="col-span-2"
                  placeholder="Es: Prysmian, Nexans"
                  disabled={loading}
                />
              </div>

              {/* Numero DDT */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="n_DDT" className="text-right">
                  N° DDT
                </Label>
                <Input
                  id="n_DDT"
                  value={formData.n_DDT}
                  onChange={(e) => handleInputChange('n_DDT', e.target.value)}
                  className="col-span-2"
                  placeholder="Es: DDT001"
                  disabled={loading}
                />
              </div>

              {/* Data DDT */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="data_DDT" className="text-right">
                  Data DDT
                </Label>
                <Input
                  id="data_DDT"
                  type="date"
                  value={formData.data_DDT}
                  onChange={(e) => handleInputChange('data_DDT', e.target.value)}
                  className="col-span-2"
                  disabled={loading}
                />
              </div>
            </div>
          </div>



          {/* Errori */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading || showConfigSelection}>
            Annulla
          </Button>
          <Button
            onClick={handleSave}
            disabled={loading || showConfigSelection || loadingConfig}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {loading ? 'Creando...' : 'Crea Bobina'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
