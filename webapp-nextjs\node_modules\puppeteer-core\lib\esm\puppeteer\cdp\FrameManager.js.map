{"version": 3, "file": "FrameManager.js", "sourceRoot": "", "sources": ["../../../../src/cdp/FrameManager.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAIH,OAAO,EAAkB,eAAe,EAAC,MAAM,sBAAsB,CAAC;AACtE,OAAO,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAE3C,OAAO,EAAC,YAAY,EAAC,MAAM,2BAA2B,CAAC;AAEvD,OAAO,EAAC,UAAU,EAAE,YAAY,EAAE,kBAAkB,EAAC,MAAM,mBAAmB,CAAC;AAC/E,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,QAAQ,EAAC,MAAM,qBAAqB,CAAC;AAC7C,OAAO,EAAC,aAAa,EAAC,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAC,WAAW,EAAC,MAAM,sBAAsB,CAAC;AAGjD,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAC,aAAa,EAAC,MAAM,iBAAiB,CAAC;AAC9C,OAAO,EAAC,mBAAmB,EAAC,MAAM,iBAAiB,CAAC;AACpD,OAAO,EAAC,0BAA0B,EAAC,MAAM,0BAA0B,CAAC;AACpE,OAAO,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AACvD,OAAO,EAAC,QAAQ,EAAC,MAAM,YAAY,CAAC;AAEpC,OAAO,EAAC,iBAAiB,EAAC,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAC,SAAS,EAAC,MAAM,gBAAgB,CAAC;AAEzC,OAAO,EAAC,UAAU,EAAE,eAAe,EAAC,MAAM,qBAAqB,CAAC;AAChE,OAAO,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AAInD,MAAM,yBAAyB,GAAG,GAAG,CAAC,CAAC,MAAM;AAE7C;;;;GAIG;AACH,MAAM,OAAO,YAAa,SAAQ,YAAgC;IAChE,KAAK,CAAU;IACf,eAAe,CAAiB;IAChC,gBAAgB,CAAkB;IAClC,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;IACpC,OAAO,CAAa;IACpB,+BAA+B,GAAG,IAAI,GAAG,EAA4B,CAAC;IACtE,SAAS,GAAG,IAAI,GAAG,EAAW,CAAC;IAE/B,UAAU,GAAG,IAAI,SAAS,EAAY,CAAC;IAEvC;;;;OAIG;IACH,uBAAuB,GAAG,IAAI,GAAG,EAAU,CAAC;IAE5C,8BAA8B,GAAG,IAAI,OAAO,EAGzC,CAAC;IAEJ,iBAAiB,CAAkB;IAEnC,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,YACE,MAAkB,EAClB,IAAa,EACb,eAAgC;QAEhC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;QAChD,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7C,IAAI,CAAC,mBAAmB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACjD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;YAC5C,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;QACD,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAO;YACpC,OAAO,EAAE,yBAAyB;YAClC,OAAO,EAAE,uBAAuB;SACjC,CAAC,CAAC;QACH,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACvD,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,YAAY,EAAE,CAAC;QAC/B,CAAC;QAAC,MAAM,CAAC;YACP,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,aAAa,CAAC,MAAkB;QACpC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,MAAM,CACJ,IAAI,CAAC,OAAO,YAAY,aAAa,EACrC,kDAAkD,CACnD,CAAC;QACF,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QAC7C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC;YACnE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACnC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAChC,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACjC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7C,IAAI,CAAC,mBAAmB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACrC,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,MAAqB;QACpD,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAEO,mBAAmB,CAAC,OAAmB;QAC7C,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAC7C,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAC9C,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,KAAK,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,8BAA8B,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YACvD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CACR,oBAAoB,EACpB,KAAK,EAAE,KAAuC,EAAE,EAAE;YAChD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CACnB,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,MAAgD,CACvD,CAAC;QACJ,CAAC,CACF,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,0BAA0B,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YACnD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,0BAA0B,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YACnD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,iCAAiC,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAC1D,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAC9C,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAkB,EAAE,KAAuB;QAC1D,IAAI,CAAC;YACH,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC3C,qEAAqE;YACrE,oEAAoE;YACpE,wEAAwE;YACxE,4DAA4D;YAC5D,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,SAAS,EAAC,EAAE,EAAE;oBACpD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;oBACzC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;gBACpC,CAAC,CAAC;gBACF,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBACtC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;gBAC/D,CAAC,CAAC;gBACF,GAAG,CAAC,KAAK;oBACP,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,CAAC,MAAM,EAAE,CAAC;oBAC3D,CAAC,CAAC,EAAE,CACL,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;oBACb,OAAO,KAAK,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBACzC,CAAC,CAAC;gBACF,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;oBAClE,OAAO,KAAK,EAAE,yBAAyB,CAAC,OAAO,CAAC,CAAC;gBACnD,CAAC,CAAC;aACH,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;YAClC,wEAAwE;YACxE,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,KAAK,CAAC,EAAE,CAAC;gBACrD,OAAO;YACT,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,SAAS;QACP,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACjD,MAAM,CAAC,SAAS,EAAE,kCAAkC,CAAC,CAAC;QACtD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,OAAe;QACnB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,OAAgB;QAC9C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5B,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;YAC9B,OAAO,MAAM,KAAK,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;QACxD,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,4BAA4B,CAAC,OAAgB;QACjD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/B,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;YAC9B,OAAO,MAAM,KAAK,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;QAC3D,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,MAAc;QAEd,MAAM,EAAC,UAAU,EAAC,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE;aACxC,OAAO,EAAE;aACT,IAAI,CAAC,uCAAuC,EAAE;YAC7C,MAAM;SACP,CAAC,CAAC;QAEL,MAAM,aAAa,GAAG,IAAI,gBAAgB,CACxC,IAAI,CAAC,SAAS,EAAE,EAChB,UAAU,EACV,MAAM,CACP,CAAC;QAEF,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;QAEpE,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;YAC9B,OAAO,MAAM,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC,CAAC,CACH,CAAC;QAEF,OAAO,EAAC,UAAU,EAAC,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,mCAAmC,CAAC,UAAkB;QAC1D,MAAM,aAAa,GAAG,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC3E,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CACb,8CAA8C,UAAU,YAAY,CACrE,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAExD,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACxB,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YACtD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO;YACT,CAAC;YACD,OAAO,KAAK;iBACT,OAAO,EAAE;iBACT,IAAI,CAAC,0CAA0C,EAAE;gBAChD,UAAU;aACX,CAAC;iBACD,KAAK,CAAC,UAAU,CAAC,CAAC;QACvB,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAED,kBAAkB,CAAC,MAAiB;QAClC,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC9C,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAG,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAG,CAAC,CAAC;QAC7C,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAG,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;IAED,2BAA2B,CAAC,MAAkB;QAC5C,IAAI,OAAO,GAAG,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO,GAAG,IAAI,0BAA0B,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACxE,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,iBAAiB,CAAC,KAAwC;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC5B,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAED,gBAAgB,CACd,OAAmB,EACnB,SAAkC;QAElC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,gBAAgB,CACnB,OAAO,EACP,SAAS,CAAC,KAAK,CAAC,EAAE,EAClB,SAAS,CAAC,KAAK,CAAC,QAAQ,CACzB,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;YAC1D,KAAK,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;YAC1C,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,gBAAgB,CACd,OAAmB,EACnB,OAAe,EACf,aAAqB;QAErB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC9C,IAAI,OAAO,IAAI,WAAW,IAAI,KAAK,CAAC,MAAM,KAAK,WAAW,EAAE,MAAM,EAAE,CAAC;gBACnE,4CAA4C;gBAC5C,4DAA4D;gBAC5D,qBAAqB;gBACrB,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO;QACT,CAAC;QAED,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,YAAiC,EACjC,cAA4C;QAE5C,MAAM,OAAO,GAAG,YAAY,CAAC,EAAE,CAAC;QAChC,MAAM,WAAW,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC;QAE3C,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE7C,iCAAiC;QACjC,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,KAAK,EAAE,CAAC;gBACV,wEAAwE;gBACxE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACnC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,iCAAiC;gBACjC,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;QAED,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACpD,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAmB,EAAE,IAAY;QAC1D,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QAED,MAAM,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE;YAC1D,MAAM,EAAE,iBAAiB,YAAY,CAAC,YAAY,EAAE;YACpD,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM,EAAE;aACV,MAAM,CAAC,KAAK,CAAC,EAAE;YACd,OAAO,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC;QAClC,CAAC,CAAC;aACD,GAAG,CAAC,KAAK,CAAC,EAAE;YACX,mEAAmE;YACnE,kBAAkB;YAClB,OAAO,OAAO;iBACX,IAAI,CAAC,0BAA0B,EAAE;gBAChC,OAAO,EAAE,KAAK,CAAC,GAAG;gBAClB,SAAS,EAAE,IAAI;gBACf,mBAAmB,EAAE,IAAI;aAC1B,CAAC;iBACD,KAAK,CAAC,UAAU,CAAC,CAAC;QACvB,CAAC,CAAC,CACL,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,+BAA+B,CAAC,OAAe,EAAE,GAAW;QAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACjE,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB,CACd,OAAe,EACf,MAA8C;QAE9C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,QAAQ;gBACX,gEAAgE;gBAChE,qCAAqC;gBACrC,kEAAkE;gBAClE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;gBACrC,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBACjD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;gBAC/C,MAAM;QACV,CAAC;IACH,CAAC;IAED,0BAA0B,CACxB,cAA4D,EAC5D,OAAmB;QAEnB,MAAM,OAAO,GAAG,cAAc,CAAC,OAAyC,CAAC;QACzE,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAM,KAAK,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC5E,IAAI,KAAgC,CAAC;QACrC,IAAI,KAAK,EAAE,CAAC;YACV,sEAAsE;YACtE,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;gBAC7B,OAAO;YACT,CAAC;YACD,IAAI,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;gBAClE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACnC,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBACtD,0EAA0E;gBAC1E,oEAAoE;gBACpE,qBAAqB;gBACrB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;QACD,sEAAsE;QACtE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAClC,KAAK,EAAE,MAAM,IAAI,IAAI,CAAC,OAAO,EAC7B,cAAc,EACd,KAAK,CACN,CAAC;QACF,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED,wBAAwB,CAAC,KAAe;QACtC,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;YACxC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;QACD,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAClD,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;CACF"}