'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { User, Cantiere } from '@/types'
import { authApi, usersApi } from '@/lib/api'

interface AuthContextType {
  user: User | null
  }

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [cantiere, setCantiere] = useState<Cantiere | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isImpersonating, setIsImpersonating] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('isImpersonating') === 'true'
    }
    return false
  })
  const [impersonatedUser, setImpersonatedUser] = useState<any | null>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('impersonatedUser')
      return stored ? JSON.parse(stored) : null
    }
    return null
  })

  const isAuthenticated = !!user || !!cantiere

  // Verifica l'autenticazione al caricamento
  useEffect(() => {
    checkAuth()
  }, [])

  // Carica il cantiere selezionato dal localStorage all'avvio (come nella webapp originale)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const cantiereId = localStorage.getItem('selectedCantiereId')
      const cantiereName = localStorage.getItem('selectedCantiereName')

      if (cantiereId && !cantiere) {
        const cantiereData = {
          id_cantiere: parseInt(cantiereId, 10)`,
          codice_univoco: '',
          id_utente: user?.id_utente || 0
        }
        setCantiere(cantiereData)
      }
    }
  }, [user, cantiere])

  const checkAuth = async () => {
    try {
      // Verifica se siamo nel browser
      if (typeof window === 'undefined') {
        setIsLoading(false)
        return
      }

      // Prima di tutto, imposta loading a true
      setIsLoading(true)

      // Pulisci eventuali token non validi o scaduti
      const token = localStorage.getItem('token')

      if (token) {
        try {
          // Verifica la validità del token
          const userData = await authApi.verifyToken()

          // Imposta i dati dell'utente come nel sistema React originale
          const userInfo = {
            id_utente: userData.user_id
          setUser(userInfo)

          // Gestisci l'impersonificazione
          const impersonatingState = userData.is_impersonated === true
          setIsImpersonating(impersonatingState)

          if (impersonatingState && userData.impersonated_id) {
            const impersonatedUserData = {
              id: userData.impersonated_id
            setImpersonatedUser(impersonatedUserData)
            if (typeof window !== 'undefined') {
              localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))
              localStorage.setItem('isImpersonating', 'true')
            }
          } else {
            setImpersonatedUser(null)
            if (typeof window !== 'undefined') {
              localStorage.removeItem('impersonatedUser')
              localStorage.removeItem('isImpersonating')
            }
          }

          // Se è un utente cantiere, gestisci i dati del cantiere
          if (userData.role === 'cantieri_user' && userData.cantiere_id) {
            const cantiereData = {
              id_cantiere: userData.cantiere_id`,
              codice_univoco: '',
              id_utente: userData.user_id
            }
            setCantiere(cantiereData)
          } else {
            // Prova a caricare i dati del cantiere dal localStorage
            const cantiereDataString = localStorage.getItem('cantiere_data')
            if (cantiereDataString) {
              try {
                const cantiereData = JSON.parse(cantiereDataString)
                setCantiere(cantiereData)
              } catch (error) {
                localStorage.removeItem('cantiere_data')
              }
            }
          }
        } catch (tokenError) {
          // Se il token non è valido, rimuovilo
          localStorage.removeItem('token')
          localStorage.removeItem('access_token')
          localStorage.removeItem('user_data')
          localStorage.removeItem('cantiere_data')
          setUser(null)
          setCantiere(null)
        }
      } else {
        setUser(null)
        setCantiere(null)
      }
    } catch (error) {
      // In caso di errore generale, assicurati che l'utente non sia autenticato
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token')
        localStorage.removeItem('access_token')
        localStorage.removeItem('user_data')
        localStorage.removeItem('cantiere_data')
      }
      setUser(null)
      setCantiere(null)
    } finally {
      // Assicurati che loading sia impostato a false alla fine
      setTimeout(() => {
        setIsLoading(false)
      }, 500) // Aggiungi un piccolo ritardo come nel sistema React originale
    }
  }

  const login = async (username: string, password: string) => {
    try {
      setIsLoading(true)
      const response = await authApi.login({ username, password })

      if (typeof window !== 'undefined') {
        // Salva il token come nel sistema React originale
        localStorage.setItem('token', response.access_token)

        // Il backend restituisce i dati dell'utente direttamente nella risposta
        const userData = {
          id_utente: response.user_id

        setUser(userData)
        setCantiere(null)

        return userData
      }
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const loginCantiere = async (codice_cantiere: string, password_cantiere: string) => {
    try {
      setIsLoading(true)
      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })

      if (typeof window !== 'undefined') {
        // Salva il token come nel sistema React originale
        localStorage.setItem('token', response.access_token)

        // Il backend restituisce i dati del cantiere direttamente nella risposta
        const cantiereData = {
          id_cantiere: response.cantiere_id

        // Salva i dati del cantiere nel localStorage
        localStorage.setItem('cantiere_data', JSON.stringify(cantiereData))

        setCantiere(cantiereData)
        setUser(null)

        return cantiereData
      }
    } catch (error) {
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const impersonateUser = async (userId: number) => {
    try {
      // Chiama l'endpoint di impersonificazione
      const response = await usersApi.impersonateUser(userId)

      if (typeof window !== 'undefined') {
        // Salva il token nel localStorage
        localStorage.setItem('token', response.access_token)

        // Salva i dati dell'utente impersonato
        const impersonatedUserData = {
          id: response.impersonated_id

        // Salva i dati dell'utente impersonato nel localStorage
        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))
        setImpersonatedUser(impersonatedUserData)

        // Imposta lo stato di impersonificazione a true
        setIsImpersonating(true)
        localStorage.setItem('isImpersonating', 'true')

        return { impersonatedUser: impersonatedUserData }
      }
    } catch (error) {
      throw error
    }
  }

  // Funzione per selezionare un cantiere (come nella webapp originale)
  const selectCantiere = (cantiere: Cantiere) => {
    if (cantiere && cantiere.id_cantiere) {
      // Usa commessa come nome del cantiere, con fallback su nome se commessa non è disponibile
      const cantiereName = cantiere.commessa || `Cantiere ${cantiere.id_cantiere}`

      // Salva nel localStorage
      localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())
      localStorage.setItem('selectedCantiereName', cantiereName)

      // Aggiorna lo stato
      setCantiere({
        ...cantiere)
    }
  }

  const logout = () => {
    if (typeof window !== 'undefined') {
      // Logout sempre completo - rimuovi tutto
      localStorage.clear() // Pulisce tutto il localStorage
      sessionStorage.clear() // Pulisce anche sessionStorage

      // Reset stati
      setUser(null)
      setCantiere(null)
      setIsImpersonating(false)
      setImpersonatedUser(null)

      // Forza reload completo della pagina per evitare cache
      window.location.replace('/login')
    }
  }

  const value: AuthContextType = {
    user,
    cantiere,
    isAuthenticated,
    isLoading,
    isImpersonating,
    impersonatedUser,
    login,
    loginCantiere,
    logout,
    checkAuth,
    impersonateUser,
    selectCantiere}

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
