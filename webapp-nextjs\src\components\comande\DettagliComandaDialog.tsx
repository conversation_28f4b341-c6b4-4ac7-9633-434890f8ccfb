'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Loader2, 
  AlertCircle, 
  ClipboardList, 
  Users, 
  Calendar, 
  Clock,
  CheckCircle,
  Play,
  Pause,
  User,
  Phone,
  Mail,
  MapPin,
  Cable,
  Activity
} from 'lucide-react'
import { comandeApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { getComandaColorClasses } from '@/utils/softColors'

interface DettagliComandaDialogProps {
  open: boolean
  console.log({
  open: boolean
  onClose: () => void
  codiceComanda: string | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface ComandaDettagli {
  codice_comanda: string
  console.log({
  codice_comanda: string
  tipo_comanda: string
  stato: string
  responsabile: string
  descrizione?: string
  console.log({
  descrizione?: string
  data_creazione: string
  data_scadenza?: string
  data_completamento?: string
  console.log({
  data_completamento?: string
  numero_componenti_squadra: number
  cavi_assegnati?: any[]
  progresso?: {
    totale: number
    console.log({
    totale: number
    completati: number
    percentuale: number
  }
  responsabile_dettagli?: {
    nome_responsabile: string
    numero_telefono?: string
    mail?: string
  }
}

export default function DettagliComandaDialog({
  open,
  onClose,
  codiceComanda,
  onSuccess,
  onError
}: DettagliComandaDialogProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [comanda, setComanda] = useState<ComandaDettagli | null>(null)

  const { cantiere } = useAuth()

  // Get cantiere ID con fallback al localStorage
  const [cantiereId, setCantiereId] = useState<number>(0)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedId = cantiere?.id_cantiere || parseInt(localStorage.getItem('selectedCantiereId') || '0')
      setCantiereId(storedId)
    }
  }, [cantiere])

  // Carica dettagli comanda quando si apre il dialog
  useEffect(() => {
    if (open && codiceComanda && cantiereId > 0) {
      loadComandaDettagli()
    }
  }, [open, codiceComanda, cantiereId])

  // Reset quando si chiude il dialog
  useEffect(() => {
    if (!open) {
      setComanda(null)
      setError('')
    }
  }, [open])

  const loadComandaDettagli = async () => {
    if (!codiceComanda) return

    try {
      setLoading(true)
      setError('')
      
      const response = await comandeApi.getComanda(cantiereId, codiceComanda)
      const comandaData = response?.data || response
      
      setComanda(comandaData)
    } catch (error: any) {
      setError('Errore durante il caricamento dei dettagli della comanda')
    } finally {
      setLoading(false)
    }
  }

  const getTipoBadge = (tipo: string) => {
    const tipoLabels: Record<string, { label: string; icon: string }> = {
      'POSA': { label: 'Posa Cavi', icon: '🔧' },
      'COLLEGAMENTO_PARTENZA': { label: 'Collegamento Partenza', icon: '🔌' },
      'COLLEGAMENTO_ARRIVO': { label: 'Collegamento Arrivo', icon: '⚡' },
      'CERTIFICAZIONE': { label: 'Certificazione', icon: '📋' }
    }
    
    const tipoInfo = tipoLabels[tipo] || { label: tipo, icon: '❓' }
    
    return (
      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
        {tipoInfo.icon} {tipoInfo.label}
      </Badge>
    )
  }

  const getStatusBadge = (stato: string) => {
    const colors = getComandaColorClasses(stato)
    return (
      <Badge className={colors.badge}>
        {stato}
      </Badge>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: '2-digit',
      console.log({
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getProgressColor = (percentuale: number) => {
    if (percentuale >= 80) return 'bg-green-500'
    if (percentuale >= 50) return 'bg-yellow-500'
    return 'bg-blue-500'
  }

  if (!codiceComanda) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ClipboardList className="h-5 w-5" />
            Dettagli Comanda {codiceComanda}
          </DialogTitle>
          <DialogDescription>
            Visualizza tutti i dettagli e lo stato della comanda
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex items-center gap-2">
                <Loader2 className="h-5 w-5 animate-spin" />
                Caricamento dettagli comanda...
              </div>
            </div>
          ) : comanda ? (
            <div className="space-y-6">
              {/* Header con info principali */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Informazioni Generali
                    </CardTitle>
                    <div className="flex gap-2">
                      {getTipoBadge(comanda.tipo_comanda)}
                      {getStatusBadge(comanda.stato)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-slate-500" />
                        <div>
                          <p className="text-sm text-slate-500">Data Creazione</p>
                          <p className="font-medium">{formatDate(comanda.data_creazione)}</p>
                        </div>
                      </div>
                      
                      {comanda.data_scadenza && (
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-slate-500" />
                          <div>
                            <p className="text-sm text-slate-500">Scadenza</p>
                            <p className="font-medium">{formatDate(comanda.data_scadenza)}</p>
                          </div>
                        </div>
                      )}
                      
                      {comanda.data_completamento && (
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <div>
                            <p className="text-sm text-slate-500">Completamento</p>
                            <p className="font-medium text-green-700">{formatDate(comanda.data_completamento)}</p>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-slate-500" />
                        <div>
                          <p className="text-sm text-slate-500">Componenti Squadra</p>
                          <p className="font-medium">{comanda.numero_componenti_squadra} persone</p>
                        </div>
                      </div>
                      
                      {comanda.descrizione && (
                        <div>
                          <p className="text-sm text-slate-500">Descrizione</p>
                          <p className="font-medium">{comanda.descrizione}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Responsabile */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Responsabile
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start gap-4">
                    <div className="flex-1">
                      <p className="font-medium text-lg">{comanda.responsabile || 'Non assegnato'}</p>
                      {comanda.responsabile_dettagli && (
                        <div className="mt-2 space-y-1 text-sm text-slate-600">
                          {comanda.responsabile_dettagli.numero_telefono && (
                            <div className="flex items-center gap-2">
                              <Phone className="h-3 w-3" />
                              {comanda.responsabile_dettagli.numero_telefono}
                            </div>
                          )}
                          {comanda.responsabile_dettagli.mail && (
                            <div className="flex items-center gap-2">
                              <Mail className="h-3 w-3" />
                              {comanda.responsabile_dettagli.mail}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Progresso */}
              {comanda.progresso && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="h-5 w-5" />
                      Progresso Lavori
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Completamento</span>
                        <span className="text-sm font-medium">{comanda.progresso.percentuale}%</span>
                      </div>
                      <Progress 
                        value={comanda.progresso.percentuale} 
                        className="h-2"
                      />
                      <div className="flex justify-between text-sm text-slate-600">
                        <span>{comanda.progresso.completati} completati</span>
                        <span>{comanda.progresso.totale} totali</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Cavi assegnati */}
              {comanda.cavi_assegnati && comanda.cavi_assegnati.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Cable className="h-5 w-5" />
                      Cavi Assegnati ({comanda.cavi_assegnati.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {comanda.cavi_assegnati.map((cavo, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-slate-50 rounded">
                          <span className="font-mono text-sm">{cavo.id_cavo || cavo}</span>
                          {cavo.stato && (
                            <Badge variant="outline" className="text-xs">
                              {cavo.stato}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-slate-500">
              Nessun dettaglio disponibile
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Chiudi
          </Button>
          {comanda && (
            <Button
              onClick={() => {
                // TODO: Aprire dialog di modifica
                onSuccess('Funzione di modifica in sviluppo')
              }}
            >
              Modifica Comanda
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
