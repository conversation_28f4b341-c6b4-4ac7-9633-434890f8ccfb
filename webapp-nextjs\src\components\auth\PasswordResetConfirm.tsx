'use client'

import React, { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Eye, EyeOff, Shield, CheckCircle, XCircle, AlertTriangle, Lock } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PasswordStrength {
  score: number
  }

interface ResetFormData {
  newPassword: string
  }

export function PasswordResetConfirm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')

  const [formData, setFormData] = useState<ResetFormData>({
    newPassword: '')
  const [showPasswords, setShowPasswords] = useState({
    new: false)
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0)
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [tokenValid, setTokenValid] = useState<boolean | null>(null)

  useEffect(() => {
    if (!token) {
      setMessage({ type: 'error', text: 'Token di reset mancante o non valido' })
      setTokenValid(false)
      return
    }

    // Qui potresti aggiungere una validazione del token lato client se necessario
    setTokenValid(true)
  }, [token])

  const validatePassword = async (password: string) => {
    if (!password) {
      setPasswordStrength({ score: 0, feedback: [], isValid: false })
      return
    }

    try {
      const response = await fetch('/api/password/validate-password', {
        method: 'POST',
        body: JSON.stringify({ password })
      })
      
      if (response.ok) {
        const result = await response.json()
        setPasswordStrength({
          score: result.strength_score)
      }
    } catch (error) {
    }
  }

  const handlePasswordChange = (field: keyof ResetFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    if (field === 'newPassword') {
      validatePassword(value)
    }
  }

  const getPasswordStrengthColor = (score: number) => {
    if (score < 2) return 'bg-red-500'
    if (score < 4) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getPasswordStrengthText = (score: number) => {
    if (score < 2) return 'Debole'
    if (score < 4) return 'Media'
    return 'Forte'
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage(null)

    try {
      // Validazioni client-side
      if (!formData.newPassword || !formData.confirmPassword) {
        throw new Error('Tutti i campi sono obbligatori')
      }

      if (formData.newPassword !== formData.confirmPassword) {
        throw new Error('Le password non corrispondono')
      }

      if (!passwordStrength.isValid) {
        throw new Error('La password non rispetta i requisiti di sicurezza')
      }

      if (!token) {
        throw new Error('Token di reset non valido')
      }

      const response = await fetch('/api/password/confirm-password-reset', {
        method: 'POST',
        body: JSON.stringify({
          token: token,
          new_password: formData.newPassword,
          confirm_password: formData.confirmPassword
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setMessage({ type: 'success', text: result.message })
        
        // Reindirizza al login dopo 3 secondi
        setTimeout(() => {
          router.push('/login')
        }, 3000)
      } else {
        throw new Error(result.detail || result.message || 'Errore durante il reset della password')
      }
    } catch (error) {
      setMessage({ 
        type: 'error')
    } finally {
      setIsLoading(false)
    }
  }

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }))
  }

  if (tokenValid === false) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl flex items-center justify-center gap-2 text-red-600">
              <XCircle className="h-6 w-6" />
              Token Non Valido
            </CardTitle>
            <CardDescription>
              Il link di reset password non è valido o è scaduto
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-4">
              Il token potrebbe essere scaduto o già utilizzato. 
              Richiedi un nuovo link di reset password.
            </p>
            <Button 
              onClick={() => router.push('/forgot-password')}
              className="bg-mariner-600 hover:bg-mariner-700"
            >
              Richiedi Nuovo Reset
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl flex items-center gap-2">
            <Lock className="h-5 w-5 text-mariner-600" />
            Reimposta Password
          </CardTitle>
          <CardDescription>
            Inserisci la tua nuova password sicura
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Nuova Password */}
            <div className="space-y-2">
              <Label htmlFor="newPassword">Nuova Password</Label>
              <div className="relative">
                <Input
                  id="newPassword"
                  type={showPasswords.new ? 'text' : 'password'}
                  value={formData.newPassword}
                  onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                  className="pr-10"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => togglePasswordVisibility('new')}
                >
                  {showPasswords.new ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </Button>
              </div>
              
              {/* Indicatore Forza Password */}
              {formData.newPassword && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Forza password:</span>
                    <span className={cn(
                      "font-medium",
                      passwordStrength.score < 2 ? "text-red-600" :
                      passwordStrength.score < 4 ? "text-yellow-600" : "text-green-600"
                    )}>
                      {getPasswordStrengthText(passwordStrength.score)}
                    </span>
                  </div>
                  <Progress 
                    value={(passwordStrength.score / 5) * 100} 
                    className="h-2"
                  />
                  {passwordStrength.feedback.length > 0 && (
                    <div className="text-sm text-gray-600">
                      <p className="font-medium mb-1">Suggerimenti:</p>
                      <ul className="list-disc list-inside space-y-1">
                        {passwordStrength.feedback.map((suggestion, index) => (
                          <li key={index}>{suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Conferma Password */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Conferma Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showPasswords.confirm ? 'text' : 'password'}
                  value={formData.confirmPassword}
                  onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                  className="pr-10"
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => togglePasswordVisibility('confirm')}
                >
                  {showPasswords.confirm ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </Button>
              </div>
              
              {/* Validazione Corrispondenza Password */}
              {formData.confirmPassword && (
                <div className="flex items-center gap-2 text-sm">
                  {formData.newPassword === formData.confirmPassword ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-green-600">Le password corrispondono</span>
                    </>
                  ) : (
                    <>
                      <XCircle className="h-4 w-4 text-red-600" />
                      <span className="text-red-600">Le password non corrispondono</span>
                    </>
                  )}
                </div>
              )}
            </div>

            {/* Messaggio di Stato */}
            {message && (
              <Alert className={cn(
                message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
              )}>
                {message.type === 'success' ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription className={cn(
                  message.type === 'success' ? 'text-green-800' : 'text-red-800'
                )}>
                  {message.text}
                  {message.type === 'success' && (
                    <div className="mt-2 text-sm">
                      Verrai reindirizzato al login tra pochi secondi...
                    </div>
                  )}
                </AlertDescription>
              </Alert>
            )}

            {/* Pulsante Submit */}
            <Button 
              type="submit" 
              className="w-full bg-mariner-600 hover:bg-mariner-700"
              disabled={isLoading || !passwordStrength.isValid || formData.newPassword !== formData.confirmPassword}
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Aggiornamento...
                </>
              ) : (
                <>
                  <Shield className="h-4 w-4 mr-2" />
                  Reimposta Password
                </>
              )}
            </Button>
          </form>

          {/* Informazioni di Sicurezza */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-2">
              <Shield className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Sicurezza</p>
                <ul className="space-y-1 text-xs">
                  <li>• Usa una password unica che non hai mai utilizzato prima</li>
                  <li>• Combina lettere maiuscole, minuscole, numeri e simboli</li>
                  <li>• Evita informazioni personali facilmente indovinabili</li>
                  <li>• Considera l'uso di un gestore di password</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
