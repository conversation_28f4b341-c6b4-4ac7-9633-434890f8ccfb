'use client'

import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Bug, Cable } from 'lucide-react'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { Cavo, ParcoCavo } from '@/types'

interface BobinaCompatibilityDebugProps {
  open: boolean
  }

export default function BobinaCompatibilityDebug({
  open,
  onClose,
  bobina
}: BobinaCompatibilityDebugProps) {
  const { cantiere } = useAuth()
  const [cavi, setCavi] = useState<Cavo[]>([])
  const [loading, setLoading] = useState(false)
  const [debugInfo, setDebugInfo] = useState<any[]>([])

  useEffect(() => {
    if (open && bobina && cantiere) {
      loadCaviForDebug()
    }
  }, [open, bobina, cantiere])

  const loadCaviForDebug = async () => {
    if (!cantiere || !bobina) return

    setLoading(true)
    try {
      const caviData = await caviApi.getCavi(cantiere.id_cantiere)
      setCavi(caviData || [])

      // Analizza compatibilità
      const debugData = (caviData || []).slice(0, 20).map(cavo => {
        const tipologiaMatch = cavo.tipologia === bobina.tipologia
        const sezioneMatch = String(cavo.sezione) === String(bobina.sezione)
        const isCompatible = tipologiaMatch && sezioneMatch

        return {
          id_cavo: cavo.id_cavo
      })

      setDebugInfo(debugData)
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }

  if (!bobina) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            Debug Compatibilità Bobina {bobina.numero_bobina}
          </DialogTitle>
          <DialogDescription>
            Analisi dettagliata della compatibilità tra bobina e cavi
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Informazioni bobina */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold mb-2">Dati Bobina</h3>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div><strong>ID:</strong> {bobina.id_bobina}</div>
              <div><strong>Numero:</strong> {bobina.numero_bobina}</div>
              <div><strong>Tipologia:</strong> "{bobina.tipologia}" (type: {typeof bobina.tipologia})</div>
              <div><strong>Sezione:</strong> "{bobina.sezione}" (type: {typeof bobina.sezione})</div>
              <div><strong>Sezione String:</strong> "{String(bobina.sezione)}"</div>
              <div><strong>Stato:</strong> {bobina.stato_bobina}</div>
            </div>
          </div>

          {/* Analisi cavi */}
          <div>
            <h3 className="font-semibold mb-2">Analisi Compatibilità Cavi (primi 20)</h3>
            {loading ? (
              <div>Caricamento...</div>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {debugInfo.map((info, index) => (
                  <div
                    key={info.id_cavo}
                    className={`p-3 rounded border text-xs ${
                      info.is_compatible ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                    }`}
                  >
                    <div className="font-semibold mb-1">
                      {info.id_cavo} - {info.is_compatible ? '✅ COMPATIBILE' : '❌ INCOMPATIBILE'}
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <div>
                        <div><strong>Cavo Tipologia:</strong> "{info.cavo_tipologia}" ({info.cavo_tipologia_type})</div>
                        <div><strong>Bobina Tipologia:</strong> "{info.bobina_tipologia}" ({info.bobina_tipologia_type})</div>
                        <div><strong>Match:</strong> {info.tipologia_match ? '✅' : '❌'}</div>
                      </div>
                      <div>
                        <div><strong>Cavo Sezione:</strong> "{info.cavo_sezione}" ({info.cavo_sezione_type})</div>
                        <div><strong>Bobina Sezione:</strong> "{info.bobina_sezione}" ({info.bobina_sezione_type})</div>
                        <div><strong>Match:</strong> {info.sezione_match ? '✅' : '❌'}</div>
                      </div>
                      <div>
                        <div><strong>Cavo Sezione String:</strong> "{info.cavo_sezione_string}"</div>
                        <div><strong>Bobina Sezione String:</strong> "{info.bobina_sezione_string}"</div>
                        <div><strong>String Match:</strong> {info.cavo_sezione_string === info.bobina_sezione_string ? '✅' : '❌'}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="flex justify-end">
          <Button onClick={onClose}>Chiudi</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
