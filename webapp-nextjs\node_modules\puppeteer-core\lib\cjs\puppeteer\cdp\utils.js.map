{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../src/cdp/utils.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAUH,sDAwDC;AAwBD,8CA0CC;AAKD,sDAyBC;AAKD,wCAsDC;AAUD,sDAEC;AArOD,+CAAiE;AACjE,iDAAyC;AAEzC;;GAEG;AACH,SAAgB,qBAAqB,CACnC,OAA0C;IAE1C,IAAI,IAAY,CAAC;IACjB,IAAI,OAAe,CAAC;IACpB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACvB,IAAI,GAAG,OAAO,CAAC;QACf,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IACzB,CAAC;SAAM,IACL,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,QAAQ;QAClC,OAAO,CAAC,SAAS,CAAC,OAAO,KAAK,OAAO,CAAC;QACxC,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAC3B,CAAC;QACD,OAAO,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;SAAM,CAAC;QACN,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAC3B,CAAC;IACD,MAAM,aAAa,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;IACjD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACjC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAClB,MAAM,UAAU,GAAG,KAAK,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC5C,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;IAEzD,mDAAmD;IACnD,UAAU,CAAC,KAAK,EAAE,CAAC;IACnB,IAAI,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC,eAAe,EAAE,CAAC;QACpE,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC;YAC5D,IACE,sBAAY,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC;gBACtC,KAAK,CAAC,GAAG,KAAK,sBAAY,CAAC,YAAY,EACvC,CAAC;gBACD,MAAM,GAAG,GAAG,sBAAY,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC1C,UAAU,CAAC,OAAO,CAChB,UAAU,KAAK,CAAC,YAAY,IAAI,GAAG,CAAC,YAAY,KAC9C,GAAG,CAAC,YACN,OAAO,GAAG,CAAC,UAAU,iBAAiB,KAAK,CAAC,UAAU,IACpD,KAAK,CAAC,YACR,GAAG,CACJ,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,IAAI,CACb,UAAU,KAAK,CAAC,YAAY,IAAI,aAAa,KAAK,KAAK,CAAC,GAAG,IACzD,KAAK,CAAC,UACR,IAAI,KAAK,CAAC,YAAY,GAAG,CAC1B,CAAC;YACJ,CAAC;YACD,IAAI,UAAU,CAAC,MAAM,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBAC/C,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,eAAe,GAAG,CAAC,OAA0C,EAAE,EAAE;IACrE,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,IAAI,OAAe,CAAC;IACpB,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IACvE,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CACnB,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,MAAM,IAAI,CAAC,EAC1C,KAAK,CAAC,MAAM,GAAG,CAAC,CACjB,CAAC;IACF,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC1B,IAAI,OAAO,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC;QACjC,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC;IACrC,CAAC;IACD,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,IAAI,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC;QAC5C,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC3C,CAAC;IACD,OAAO,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;AACzB,CAAC,CAAC;AAEF;;GAEG;AACH,SAAgB,iBAAiB,CAC/B,OAA0C;IAE1C,IAAI,IAAY,CAAC;IACjB,IAAI,OAAe,CAAC;IACpB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QACvB,IAAI,GAAG,OAAO,CAAC;QACf,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IACzB,CAAC;SAAM,IACL,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,QAAQ;QAClC,OAAO,CAAC,SAAS,CAAC,OAAO,KAAK,OAAO,CAAC;QACxC,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,EAC3B,CAAC;QACD,OAAO,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAClD,CAAC;SAAM,CAAC;QACN,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACnB,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAC3B,CAAC;IACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IACjC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAElB,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;IACvD,MAAM,YAAY,GAAG,KAAK,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;IAEvE,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;QACvB,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YAClD,4DAA4D;YAC5D,UAAU,CAAC,IAAI,CACb,UAAU,KAAK,CAAC,YAAY,IAAI,aAAa,KAAK,KAAK,CAAC,GAAG,IACzD,KAAK,CAAC,UAAU,GAAG,CACrB,IAAI,KAAK,CAAC,YAAY,GAAG,CAAC,GAAG,CAC9B,CAAC;YACF,IAAI,UAAU,CAAC,MAAM,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;gBAC/C,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1D,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CACnC,YAA2C;IAE3C,IAAA,kBAAM,EAAC,CAAC,YAAY,CAAC,QAAQ,EAAE,6CAA6C,CAAC,CAAC;IAC9E,IAAI,YAAY,CAAC,mBAAmB,EAAE,CAAC;QACrC,IAAI,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;QACnE,CAAC;QACD,QAAQ,YAAY,CAAC,mBAAmB,EAAE,CAAC;YACzC,KAAK,IAAI;gBACP,OAAO,CAAC,CAAC,CAAC;YACZ,KAAK,KAAK;gBACR,OAAO,GAAG,CAAC;YACb,KAAK,UAAU;gBACb,OAAO,QAAQ,CAAC;YAClB,KAAK,WAAW;gBACd,OAAO,CAAC,QAAQ,CAAC;YACnB;gBACE,MAAM,IAAI,KAAK,CACb,oCAAoC;oBAClC,YAAY,CAAC,mBAAmB,CACnC,CAAC;QACN,CAAC;IACH,CAAC;IACD,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAED;;GAEG;AACH,SAAgB,cAAc,CAC5B,IAAY,EACZ,IAAY,EACZ,MAAc;IAEd,kEAAkE;IAClE,2EAA2E;IAC3E,qCAAqC;IACrC,4CAA4C;IAC5C,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;QACrB,OAAO;IACT,CAAC;IAED,uDAAuD;IACvD,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;QACxB,CAAC,IAAI,CAAC,CAAC,GAAG,IAAe;YACvB,iCAAiC;YACjC,4CAA4C;YAC5C,MAAM,aAAa,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;YACvC,aAAa,CAAC,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;YACjC,aAAa,CAAC,SAAS,KAAK,IAAI,GAAG,EAAE,CAAC;YAEtC,MAAM,GAAG,GAAG,CAAC,aAAa,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC7C,aAAa,CAAC,OAAO,GAAG,GAAG,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAElC,4CAA4C;YAC5C,8CAA8C;YAC9C,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,CACvB,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI;gBACJ,IAAI;gBACJ,GAAG;gBACH,IAAI;gBACJ,SAAS,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBAC5B,OAAO,KAAK,YAAY,IAAI,CAAC;gBAC/B,CAAC,CAAC;aACH,CAAC,CACH,CAAC;YAEF,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE;oBAC/B,OAAO,CAAC,KAAc;wBACpB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;wBAC/B,OAAO,CAAC,KAAK,CAAC,CAAC;oBACjB,CAAC;oBACD,MAAM,CAAC,KAAe;wBACpB,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;wBAC/B,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACU,QAAA,kBAAkB,GAAG,YAAY,CAAC;AAE/C;;GAEG;AACH,SAAgB,qBAAqB,CAAC,IAAY,EAAE,IAAY;IAC9D,OAAO,IAAA,0BAAgB,EAAC,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,0BAAkB,CAAC,CAAC;AAC1E,CAAC"}