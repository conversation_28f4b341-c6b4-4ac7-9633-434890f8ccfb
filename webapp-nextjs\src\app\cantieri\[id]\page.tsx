'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { cantieriApi } from '@/lib/api'
import { Cantiere } from '@/types'
import {
  ArrowLeft,
  Loader2,
  AlertCircle
} from 'lucide-react'

export default function CantierePage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const cantiereId = parseInt(params.id as string)
  
  const [cantiere, setCantiere] = useState<Cantiere | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated && cantiereId) {
      loadCantiere()
    }
  }, [isAuthenticated, cantiereId])

  const loadCantiere = async () => {
    try {
      setLoading(true)
      const data = await cantieriApi.getCantiere(cantiereId)
      setCantiere(data)
      
      // Salva il cantiere selezionato nel localStorage
      localStorage.setItem('selectedCantiereId', cantiereId.toString())
      localStorage.setItem('selectedCantiereName', data.commessa)
    } catch (error) {
      console.error('Errore nel caricamento cantiere:', error)
      setError('Errore nel caricamento del cantiere')
    } finally {
      setLoading(false)
    }
  }

  const handleBackToCantieri = () => {
    router.push('/cantieri')
  }

  // Reindirizza automaticamente alla pagina di visualizzazione cavi come nella webapp originale
  useEffect(() => {
    if (cantiere && !loading) {
      router.push('/cavi')
    }
  }, [cantiere, loading, router])

  if (isLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (error || !cantiere) {
    return (
      <div className="max-w-[90%] mx-auto p-6">
        <div className="mb-4 p-4 border border-red-200 rounded-lg bg-red-50">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
            <span className="text-red-800">{error || 'Cantiere non trovato'}</span>
          </div>
        </div>
        <Button onClick={handleBackToCantieri}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Torna alla Lista Cantieri
        </Button>
      </div>
    )
  }

  // Mostra un loading mentre reindirizza
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <p className="text-muted-foreground">Caricamento gestione cavi...</p>
      </div>
    </div>
  )
}
