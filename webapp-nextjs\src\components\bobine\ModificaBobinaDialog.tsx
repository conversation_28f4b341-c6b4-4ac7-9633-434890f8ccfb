'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Package, Info } from 'lucide-react'
import { parcoCaviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface Bobina {
  id_bobina: string
  console.log({
  id_bobina: string
  numero_bobina: string
  utility: string
  tipologia: string
  sezione: string // formazione nel sistema
  metri_totali: number
  metri_residui: number
  stato_bobina: string
  ubicazione_bobina?: string
  fornitore?: string
  n_DDT?: string
  data_DDT?: string
  console.log({
  data_DDT?: string
  configurazione: string
}

interface FormData {
  numero_bobina: string
  console.log({
  numero_bobina: string
  utility: string
  tipologia: string
  sezione: string
  metri_totali: string
  metri_residui: string
  stato_bobina: string
  ubicazione_bobina: string
  fornitore: string
  n_DDT: string
  data_DDT: string
  configurazione: string
}

interface FormErrors {
  [key: string]: string
}

interface FormWarnings {
  [key: string]: string
}

interface ModificaBobinaDialogProps {
  open: boolean
  console.log({
  open: boolean
  onClose: () => void
  bobina: Bobina | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export default function ModificaBobinaDialog({
  open,
  onClose,
  bobina,
  onSuccess,
  onError
}: ModificaBobinaDialogProps) {
  const { cantiere } = useAuth()
  
  // Stati per il form
  const [formData, setFormData] = useState<FormData>({
    numero_bobina: '',
    console.log({
    numero_bobina: '',
    utility: '',
    tipologia: '',
    sezione: '',
    metri_totali: '',
    metri_residui: '',
    stato_bobina: '',
    ubicazione_bobina: '',
    fornitore: '',
    n_DDT: '',
    data_DDT: '',
    configurazione: ''
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [formWarnings, setFormWarnings] = useState<FormWarnings>({})
  const [saving, setSaving] = useState(false)

  // Carica dati bobina quando si apre il dialog
  useEffect(() => {
    if (open && bobina) {
      setFormData({
        numero_bobina: bobina.numero_bobina || '',
        console.log({
        numero_bobina: bobina.numero_bobina || '',
        utility: bobina.utility || '',
        tipologia: bobina.tipologia || '',
        sezione: bobina.sezione || '',
        metri_totali: bobina.metri_totali?.toString() || '',
        metri_residui: bobina.metri_residui?.toString() || '',
        stato_bobina: bobina.stato_bobina || '',
        ubicazione_bobina: bobina.ubicazione_bobina || '',
        fornitore: bobina.fornitore || '',
        n_DDT: bobina.n_DDT || '',
        data_DDT: bobina.data_DDT || '',
        configurazione: bobina.configurazione || ''
      })
      setFormErrors({})
      setFormWarnings({})
    }
  }, [open, bobina])

  // Validazione real-time
  useEffect(() => {
    validateForm()
  }, [formData])

  const validateForm = () => {
    const errors: FormErrors = {}
    const warnings: FormWarnings = {}

    // Validazioni obbligatorie
    if (!formData.utility.trim()) {
      errors.utility = 'Utility è obbligatoria'
    }
    if (!formData.tipologia.trim()) {
      errors.tipologia = 'Tipologia è obbligatoria'
    }
    if (!formData.sezione.trim()) {
      errors.sezione = 'Formazione è obbligatoria'
    }
    if (!formData.metri_totali.trim()) {
      errors.metri_totali = 'Metri totali sono obbligatori'
    } else {
      const metri = parseFloat(formData.metri_totali)
      if (isNaN(metri) || metri <= 0) {
        errors.metri_totali = 'Inserire un valore numerico valido maggiore di 0'
      }
    }

    // Validazione data DDT
    if (formData.data_DDT && !/^\d{4}-\d{2}-\d{2}$/.test(formData.data_DDT)) {
      errors.data_DDT = 'Formato data non valido (YYYY-MM-DD)'
    }

    setFormErrors(errors)
    setFormWarnings(warnings)
  }

  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value }
      
      // Se cambiano i metri totali, ricalcola i metri residui
      if (field === 'metri_totali' && bobina) {
        const nuoviMetriTotali = parseFloat(value) || 0
        const metriInstallati = bobina.metri_totali - bobina.metri_residui
        const nuoviMetriResidui = Math.max(0, nuoviMetriTotali - metriInstallati)
        newData.metri_residui = nuoviMetriResidui.toString()
      }
      
      return newData
    })
  }

  // Verifica se la bobina può essere modificata
  const canModifyBobina = () => {
    if (!bobina) return false
    
    // Bobine OVER possono modificare solo alcuni campi
    if (bobina.stato_bobina === 'Over') {
      return true // Può modificare fornitore, ubicazione, DDT
    }
    
    // Bobine disponibili possono modificare tutto
    return bobina.stato_bobina === 'Disponibile'
  }

  // Verifica se un campo specifico può essere modificato
  const canModifyField = (field: string) => {
    if (!bobina) return false
    
    // Campi sempre modificabili
    const alwaysModifiable = ['fornitore', 'ubicazione_bobina', 'n_DDT', 'data_DDT']
    if (alwaysModifiable.includes(field)) {
      return true
    }
    
    // Altri campi solo se bobina è disponibile
    return bobina.stato_bobina === 'Disponibile'
  }

  const handleSave = async () => {
    if (!bobina || !cantiere) return

    if (Object.keys(formErrors).length > 0) {
      return
    }

    if (!canModifyBobina()) {
      onError('La bobina non può essere modificata nel suo stato attuale')
      return
    }

    try {
      setSaving(true)

      // Prepara i dati per l'API
      const updateData = {
        utility: formData.utility,
        console.log({
        utility: formData.utility,
        tipologia: formData.tipologia,
        sezione: formData.sezione, // sezione nel DB = formazione nel sistema
        metri_totali: parseFloat(formData.metri_totali),
        ubicazione_bobina: formData.ubicazione_bobina,
        fornitore: formData.fornitore,
        n_DDT: formData.n_DDT,
        data_DDT: formData.data_DDT || null
      }

      // Aggiorna bobina tramite API
      await parcoCaviApi.updateBobina(cantiere.id_cantiere, bobina.id_bobina, updateData)

      onSuccess(`Bobina ${bobina.numero_bobina} aggiornata con successo`)
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la modifica della bobina'
      onError(errorMessage)
    } finally {
      setSaving(false)
    }
  }

  const handleClose = () => {
    if (!saving) {
      setFormData({
        numero_bobina: '',
        console.log({
        numero_bobina: '',
        utility: '',
        tipologia: '',
        sezione: '',
        metri_totali: '',
        metri_residui: '',
        stato_bobina: '',
        ubicazione_bobina: '',
        fornitore: '',
        n_DDT: '',
        data_DDT: '',
        configurazione: ''
      })
      setFormErrors({})
      setFormWarnings({})
      onClose()
    }
  }

  if (!bobina) return null

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Modifica Bobina
          </DialogTitle>
          <DialogDescription>
            Modifica i dati della bobina {bobina.numero_bobina}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Alert condizioni modifica */}
          <Alert className="border-blue-200 bg-blue-50">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <div className="font-semibold mb-1">Condizioni per la modifica:</div>
              <ul className="list-disc list-inside text-sm space-y-1">
                <li>La bobina deve essere nello stato "Disponibile"</li>
                <li>La bobina non deve essere associata a nessun cavo</li>
                <li>Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente</li>
              </ul>
              <div className="mt-2 text-sm">
                <strong>Stato attuale:</strong> {bobina.stato_bobina}
              </div>
            </AlertDescription>
          </Alert>

          {/* Warning se ci sono avvisi */}
          {Object.keys(formWarnings).length > 0 && (
            <Alert className="border-amber-200 bg-amber-50">
              <AlertCircle className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                <div className="font-semibold">Attenzione:</div>
                <ul className="list-disc list-inside text-sm">
                  {Object.values(formWarnings).map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Form a due colonne */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* ID Bobina (non modificabile) */}
            <div className="space-y-2">
              <Label htmlFor="numero_bobina">ID Bobina</Label>
              <Input
                id="numero_bobina"
                value={formData.numero_bobina}
                disabled={true}
                className="bg-gray-100 font-semibold"
              />
            </div>

            {/* Utility */}
            <div className="space-y-2">
              <Label htmlFor="utility">Utility *</Label>
              <Input
                id="utility"
                value={formData.utility}
                onChange={(e) => handleFormChange('utility', e.target.value)}
                disabled={saving || !canModifyField('utility')}
                className={formErrors.utility ? 'border-red-500' : ''}
              />
              {formErrors.utility && (
                <p className="text-sm text-red-600">{formErrors.utility}</p>
              )}
            </div>

            {/* Tipologia */}
            <div className="space-y-2">
              <Label htmlFor="tipologia">Tipologia *</Label>
              <Input
                id="tipologia"
                value={formData.tipologia}
                onChange={(e) => handleFormChange('tipologia', e.target.value)}
                disabled={saving || !canModifyField('tipologia')}
                className={formErrors.tipologia ? 'border-red-500' : ''}
              />
              {formErrors.tipologia && (
                <p className="text-sm text-red-600">{formErrors.tipologia}</p>
              )}
            </div>

            {/* Formazione */}
            <div className="space-y-2">
              <Label htmlFor="sezione">Formazione *</Label>
              <Input
                id="sezione"
                value={formData.sezione}
                onChange={(e) => handleFormChange('sezione', e.target.value)}
                disabled={saving || !canModifyField('sezione')}
                className={formErrors.sezione ? 'border-red-500' : ''}
              />
              {formErrors.sezione && (
                <p className="text-sm text-red-600">{formErrors.sezione}</p>
              )}
            </div>

            {/* Metri Totali */}
            <div className="space-y-2">
              <Label htmlFor="metri_totali">Metri Totali *</Label>
              <Input
                id="metri_totali"
                type="number"
                value={formData.metri_totali}
                onChange={(e) => handleFormChange('metri_totali', e.target.value)}
                disabled={saving || !canModifyField('metri_totali')}
                className={formErrors.metri_totali ? 'border-red-500' : ''}
                step="0.1"
                min="0"
              />
              {formErrors.metri_totali && (
                <p className="text-sm text-red-600">{formErrors.metri_totali}</p>
              )}
            </div>

            {/* Metri Residui (non modificabile) */}
            <div className="space-y-2">
              <Label htmlFor="metri_residui">Metri Residui</Label>
              <Input
                id="metri_residui"
                type="number"
                value={formData.metri_residui}
                disabled={true}
                className="bg-gray-100"
              />
              <p className="text-sm text-gray-500">I metri residui non possono essere modificati direttamente</p>
            </div>

            {/* Stato Bobina (non modificabile) */}
            <div className="space-y-2">
              <Label htmlFor="stato_bobina">Stato Bobina</Label>
              <Select value={formData.stato_bobina} disabled={true}>
                <SelectTrigger className="bg-gray-100">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Disponibile">Disponibile</SelectItem>
                  <SelectItem value="In uso">In uso</SelectItem>
                  <SelectItem value="Terminata">Terminata</SelectItem>
                  <SelectItem value="Danneggiata">Danneggiata</SelectItem>
                  <SelectItem value="Over">Over</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Ubicazione Bobina */}
            <div className="space-y-2">
              <Label htmlFor="ubicazione_bobina">Ubicazione Bobina</Label>
              <Input
                id="ubicazione_bobina"
                value={formData.ubicazione_bobina}
                onChange={(e) => handleFormChange('ubicazione_bobina', e.target.value)}
                disabled={saving}
              />
            </div>

            {/* Fornitore */}
            <div className="space-y-2">
              <Label htmlFor="fornitore">Fornitore</Label>
              <Input
                id="fornitore"
                value={formData.fornitore}
                onChange={(e) => handleFormChange('fornitore', e.target.value)}
                disabled={saving}
              />
            </div>

            {/* Numero DDT */}
            <div className="space-y-2">
              <Label htmlFor="n_DDT">Numero DDT</Label>
              <Input
                id="n_DDT"
                value={formData.n_DDT}
                onChange={(e) => handleFormChange('n_DDT', e.target.value)}
                disabled={saving}
              />
            </div>

            {/* Data DDT */}
            <div className="space-y-2">
              <Label htmlFor="data_DDT">Data DDT (YYYY-MM-DD)</Label>
              <Input
                id="data_DDT"
                type="date"
                value={formData.data_DDT}
                onChange={(e) => handleFormChange('data_DDT', e.target.value)}
                disabled={saving}
                className={formErrors.data_DDT ? 'border-red-500' : ''}
              />
              {formErrors.data_DDT && (
                <p className="text-sm text-red-600">{formErrors.data_DDT}</p>
              )}
            </div>

            {/* Modalità Numerazione (non modificabile) */}
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="configurazione">Modalità Numerazione</Label>
              <Input
                id="configurazione"
                value={formData.configurazione === 's' ? 'Automatica' : 'Manuale'}
                disabled={true}
                className="bg-gray-100"
              />
              <p className="text-sm text-gray-500">
                {formData.configurazione === 's'
                  ? 'Numerazione progressiva automatica (1, 2, 3, ...)'
                  : 'Inserimento manuale dell\'ID bobina (es. A123, SPEC01, ...)'}
              </p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={saving}>
            Annulla
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={saving || Object.keys(formErrors).length > 0 || !canModifyBobina()}
            className="bg-mariner-600 hover:bg-mariner-700"
          >
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Salva
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
