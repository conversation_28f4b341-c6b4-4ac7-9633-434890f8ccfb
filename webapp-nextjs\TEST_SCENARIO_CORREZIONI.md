# Test Scenario - Correzioni Aggiungi Cavi

## ✅ **CORREZIONI APPLICATE AL FILE CORRETTO**

**File corretto**: `AggiungiCaviDialogSimple.tsx` (quello effettivamente in uso)
**File rimossi**: `AggiungiCaviDialog.tsx`, `AggiungiCaviDialogTest.tsx` (non utilizzati)

## 🧪 **Scenario Utente Riportato**

### **Setup**:
- **<PERSON><PERSON>**: 300m residui
- **Cavi Selezionati**: 4 cavi
- **Metri Inseriti**: 50, 50, 201, [quarto cavo]

### **Comportamento Atteso CORRETTO**:
1. **Cavo 1**: 50m → ✅ OK (residui: 250m)
2. **Cavo 2**: 50m → ✅ OK (residui: 200m)
3. **Cavo 3**: 201m → ✅ **CAUSA OVER** (residui: -1m) → **SALVATO con force_over=true**
4. **Cavo 4**: qualsiasi → 🚫 **BLOCCATO** (bobina già OVER)

### **Salvataggio**:
- ✅ **Salvati**: Cavo 1 (50m), Cavo 2 (50m), **Cavo 3 (201m con force_over=true)**
- ❌ **NON Salvati**: Cavo 4 (qualsiasi valore)

## 🔧 **Correzioni Implementate**

### **1. Logica OVER Corretta**
```typescript
// PRIMA (❌): Bloccava il cavo che causa OVER
if (metriResiduiSimulati - metri < 0) {
  caviBloccati.push(cavo.id_cavo) // ❌ Sbagliato!
}

// DOPO (✅): Salva il cavo che causa OVER, blocca i successivi
if (bobinaGiaOver) {
  caviBloccati.push(cavo.id_cavo) // Blocca se bobina già OVER
} else if (metriResiduiSimulati - metri < 0) {
  caviValidi.push(cavo.id_cavo)   // ✅ Salva cavo che causa OVER
  bobinaGiaOver = true            // ✅ Marca bobina come OVER
  metriResiduiSimulati -= metri
}
```

### **2. Filtro Salvataggio Rigoroso**
```typescript
// PRIMA (❌): Logica complessa e confusa
return metri > 0 && caviValidi.includes(cavo.id_cavo) && !caviBloccati.includes(cavo.id_cavo)

// DOPO (✅): Logica semplice e chiara
const isBlocked = caviBloccati.includes(cavo.id_cavo)
return metri > 0 && !isBlocked
```

### **3. Force Over Progressivo**
```typescript
// PRIMA (❌): Basato sui metri residui originali
const isSingleCavoOver = metriPosati > metriResiduiBobina

// DOPO (✅): Basato sui metri residui progressivi
let metriResiduiProgressivi = bobina?.metri_residui || 0
for (const cavo of caviDaSalvare) {
  const isSingleCavoOver = metriPosati > metriResiduiProgressivi
  // ... salva cavo
  metriResiduiProgressivi -= metriPosati
}
```

### **4. Feedback Visivo Migliorato**
```typescript
// Nuovo indicatore metri residui in tempo reale
const { metriResiduiSimulati } = calculateProgressiveMeters()
const metriUsati = (bobina?.metri_residui || 0) - metriResiduiSimulati
const isOverState = metriResiduiSimulati < 0

<span className={isOverState ? 'text-red-600 font-medium' : 'text-gray-500'}>
  Usati: {metriUsati}m / {bobina?.metri_residui || 0}m
  {isOverState && ` (OVER: ${Math.abs(metriResiduiSimulati)}m)`}
</span>
```

## 🎯 **Test del Nuovo Comportamento**

### **Scenario 1: Inserimento Normale**
- Bobina: 300m
- Cavi: 50m, 50m, 100m, 90m
- **Risultato**: Tutti salvati ✅

### **Scenario 2: OVER nel Mezzo**
- Bobina: 300m
- Cavi: 50m, 50m, 201m, 50m
- **Risultato**:
  - ✅ Salvati: Cavo 1 (50m), Cavo 2 (50m), **Cavo 3 (201m con force_over)**
  - ❌ Bloccati: Cavo 4 (50m)

### **Scenario 3: Modifica Real-time**
- Bobina: 300m
- Cavi: 50m, 50m, 201m → **Modifica a 150m**, 50m
- **Risultato**: Tutti salvati ✅

## 🚨 **Punti di Attenzione**

### **1. Ordine di Inserimento**
- Il sistema ora rispetta l'ordine di selezione
- I metri vengono calcolati progressivamente
- I cavi successivi a uno OVER vengono bloccati

### **2. Blocco Dinamico**
- I cavi si bloccano/sbloccano in tempo reale
- La modifica di un cavo ricalcola tutto
- Feedback visivo immediato

### **3. Salvataggio Sicuro**
- Solo i cavi NON bloccati vengono salvati
- Force over calcolato progressivamente
- Nessun errore 400 dal backend

## 🔍 **Debug Info**

### **Console Logs Aggiunti**:
```typescript
console.log(`💾 Salvando cavo ${cavo.id_cavo}:`, {
  metriPosati,
  metriResiduiProgressivi,
  isSingleCavoOver,
  isIncompatible,
  needsForceOver
})
```

### **Verifica Visiva**:
- Badge "OVER" per cavi bloccati
- Input disabilitati per cavi bloccati
- Indicatore metri usati/residui in tempo reale
- Colore rosso per stato OVER

## ✅ **Risultato Atteso**

Nel tuo scenario (300m, cavi: 50, 50, 201, X):
1. **Cavo 3 (201m)** → ✅ Salvato con force_over=true (causa OVER)
2. **Cavo 4** → ❌ Bloccato (bobina già OVER)
3. **Nessun errore 400** dal backend
4. **Cavi 1, 2 e 3** salvati correttamente

Il sistema ora dovrebbe funzionare correttamente senza errori backend!
