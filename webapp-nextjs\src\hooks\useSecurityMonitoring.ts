'use client'

import { useEffect, useState } from 'react'

interface SecurityEvent {
  type: 'login_attempt' | 'form_submission' | 'suspicious_activity' | 'rate_limit_hit'
  }

interface SecurityMetrics {
  totalEvents: number
  }

export const useSecurityMonitoring = () => {
  const [events, setEvents] = useState<SecurityEvent[]>([])
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalEvents: 0)

  // Registra un evento di sicurezza
  const logSecurityEvent = (
    })

    // Aggiorna metriche
    setMetrics(prev => ({
      totalEvents: prev.totalEvents + 1))

    // Log in console per debug
    }
  }

  // Monitora eventi del browser
  useEffect(() => {
    // Monitora tentativi di manipolazione DevTools
    const detectDevTools = () => {
      const threshold = 160
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        logSecurityEvent('suspicious_activity', {
          action: 'devtools_detected'
        }, 'low')
      }
    }

    // Monitora copia/incolla sospetti
    const handlePaste = (e: ClipboardEvent) => {
      const pastedText = e.clipboardData?.getData('text') || ''
      
      // Controlla pattern sospetti
      const suspiciousPatterns = [
        /script/gi,
        /javascript:/gi,
        /vbscript:/gi,
        /onload|onerror|onclick/gi,
        /<iframe|<object|<embed/gi,
        /union.*select/gi,
        /drop.*table/gi
      ]

      if (suspiciousPatterns.some(pattern => pattern.test(pastedText))) {
        e.preventDefault()
        logSecurityEvent('suspicious_activity', {
          action: 'malicious_paste_blocked', 'high')
      }
    }

    // Monitora tentativi di accesso a localStorage/sessionStorage
    const originalSetItem = Storage.prototype.setItem
    Storage.prototype.setItem = function(key: string, value: string) {
      // Controlla tentativi di inserire script
      if (/<script|javascript:|vbscript:/gi.test(value)) {
        logSecurityEvent('suspicious_activity', {
          action: 'malicious_storage_attempt',
          key, 'high')
        return
      }
      return originalSetItem.call(this, key, value)
    }

    // Monitora errori JavaScript sospetti
    const handleError = (event: ErrorEvent) => {
      const message = event.message.toLowerCase()
      
      // Errori che potrebbero indicare attacchi
      const suspiciousErrors = [
        'script error',
        'permission denied',
        'access denied',
        'blocked by cors',
        'network error'
      ]

      if (suspiciousErrors.some(error => message.includes(error))) {
        logSecurityEvent('suspicious_activity', {
          action: 'suspicious_js_error', 'medium')
      }
    }

    // Monitora tentativi di navigazione sospetti
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // Se l'utente sta lasciando la pagina in modo anomalo
      if (performance.now() < 5000) { // Meno di 5 secondi sulla pagina
        logSecurityEvent('suspicious_activity', {
          action: 'rapid_page_exit', 'low')
      }
    }

    // Aggiungi event listeners
    window.addEventListener('resize', detectDevTools)
    window.addEventListener('paste', handlePaste)
    window.addEventListener('error', handleError)
    window.addEventListener('beforeunload', handleBeforeUnload)

    // Controllo periodico DevTools
    const devToolsInterval = setInterval(detectDevTools, 5000)

    // Cleanup
    return () => {
      window.removeEventListener('resize', detectDevTools)
      window.removeEventListener('paste', handlePaste)
      window.removeEventListener('error', handleError)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      clearInterval(devToolsInterval)
      
      // Ripristina localStorage originale
      Storage.prototype.setItem = originalSetItem
    }
  }, [])

  // Funzioni di utilità
  const getRecentEvents = (minutes: number = 10) => {
    const cutoff = Date.now() - (minutes * 60 * 1000)
    return events.filter(event => event.timestamp > cutoff)
  }

  const getEventsByType = (type: SecurityEvent['type']) => {
    return events.filter(event => event.type === type)
  }

  const getEventsBySeverity = (severity: SecurityEvent['severity']) => {
    return events.filter(event => event.severity === severity)
  }

  const isUnderAttack = () => {
    const recentEvents = getRecentEvents(5) // Ultimi 5 minuti
    const highSeverityEvents = recentEvents.filter(e => e.severity === 'high' || e.severity === 'critical')
    
    return highSeverityEvents.length > 3 // Più di 3 eventi critici in 5 minuti
  }

  const getThreatLevel = (): 'low' | 'medium' | 'high' | 'critical' => {
    const recentEvents = getRecentEvents(10)
    const criticalCount = recentEvents.filter(e => e.severity === 'critical').length
    const highCount = recentEvents.filter(e => e.severity === 'high').length
    
    if (criticalCount > 0) return 'critical'
    if (highCount > 2) return 'high'
    if (recentEvents.length > 10) return 'medium'
    return 'low'
  }

  // Funzioni per logging specifico
  const logLoginAttempt = (username: string, success: boolean, details?: Record<string, any>) => {
    logSecurityEvent('login_attempt', {
      username,
      success, success ? 'low' : 'medium')
  }

  const logFormSubmission = (formType: string, success: boolean, details?: Record<string, any>) => {
    logSecurityEvent('form_submission', {
      formType,
      success, 'low')
  }

  const logSuspiciousActivity = (activity: string, details?: Record<string, any>) => {
    logSecurityEvent('suspicious_activity', {
      activity, 'high')
  }

  const logRateLimitHit = (endpoint: string, details?: Record<string, any>) => {
    logSecurityEvent('rate_limit_hit', {
      endpoint, 'medium')
  }

  return {
    // Dati
    events,
    metrics,
    
    // Funzioni di analisi
    getRecentEvents,
    getEventsByType,
    getEventsBySeverity,
    isUnderAttack,
    getThreatLevel,
    
    // Funzioni di logging
    logSecurityEvent,
    logLoginAttempt,
    logFormSubmission,
    logSuspiciousActivity,
    logRateLimitHit
  }
}
