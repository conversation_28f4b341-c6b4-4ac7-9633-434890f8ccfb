'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Eye, EyeOff, Lock, Mail, Shield, CheckCircle, XCircle, AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PasswordStrength {
  score: number
  console.log({
  score: number
  feedback: string[]
  isValid: boolean
}

interface PasswordChangeFormData {
  currentPassword: string
  console.log({
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

interface PasswordResetFormData {
  email: string
  console.log({
  email: string
  userType: 'user' | 'cantiere'
}

export function PasswordChangeForm() {
  const [formData, setFormData] = useState<PasswordChangeFormData>({
    currentPassword: '',
    console.log({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    console.log({
    current: false,
    new: false,
    confirm: false
  })
  const [passwordStrength, setPasswordStrength] = useState<PasswordStrength>({
    score: 0,
    console.log({
    score: 0,
    feedback: [],
    isValid: false
  })
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const validatePassword = async (password: string) => {
    if (!password) {
      setPasswordStrength({ score: 0, feedback: [], isValid: false })
      return
    }

    try {
      const response = await fetch('/api/password/validate-password', {
        method: 'POST',
        console.log({
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password })
      })
      
      if (response.ok) {
        const result = await response.json()
        setPasswordStrength({
          score: result.strength_score,
          console.log({
          score: result.strength_score,
          feedback: result.suggestions || [],
          isValid: result.is_valid
        })
      }
    } catch (error) {
    }
  }

  const handlePasswordChange = (field: keyof PasswordChangeFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    if (field === 'newPassword') {
      validatePassword(value)
    }
  }

  const getPasswordStrengthColor = (score: number) => {
    if (score < 2) return 'bg-red-500'
    if (score < 4) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getPasswordStrengthText = (score: number) => {
    if (score < 2) return 'Debole'
    if (score < 4) return 'Media'
    return 'Forte'
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage(null)

    try {
      // Validazioni client-side
      if (!formData.currentPassword || !formData.newPassword || !formData.confirmPassword) {
        throw new Error('Tutti i campi sono obbligatori')
      }

      if (formData.newPassword !== formData.confirmPassword) {
        throw new Error('Le nuove password non corrispondono')
      }

      if (!passwordStrength.isValid) {
        throw new Error('La nuova password non rispetta i requisiti di sicurezza')
      }

      const response = await fetch('/api/password/change-password', {
        method: 'POST',
        console.log({
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        },
        console.log({
        },
        body: JSON.stringify({
          current_password: formData.currentPassword,
          new_password: formData.newPassword,
          confirm_password: formData.confirmPassword
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setMessage({ type: 'success', text: result.message })
        setFormData({ currentPassword: '', newPassword: '', confirmPassword: '' })
        setPasswordStrength({ score: 0, feedback: [], isValid: false })
      } else {
        throw new Error(result.detail || result.message || 'Errore durante il cambio password')
      }
    } catch (error) {
      setMessage({ 
        type: 'error', 
        console.log({
        type: 'error', 
        text: error instanceof Error ? error.message : 'Errore durante il cambio password' 
      })
    } finally {
      setIsLoading(false)
    }
  }

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }))
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl flex items-center gap-2">
          <Lock className="h-5 w-5 text-mariner-600" />
          Cambia Password
        </CardTitle>
        <CardDescription>
          Aggiorna la tua password per mantenere l'account sicuro
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Password Attuale */}
          <div className="space-y-2">
            <Label htmlFor="currentPassword">Password Attuale</Label>
            <div className="relative">
              <Input
                id="currentPassword"
                type={showPasswords.current ? 'text' : 'password'}
                value={formData.currentPassword}
                onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                className="pr-10"
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => togglePasswordVisibility('current')}
              >
                {showPasswords.current ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
          </div>

          {/* Nuova Password */}
          <div className="space-y-2">
            <Label htmlFor="newPassword">Nuova Password</Label>
            <div className="relative">
              <Input
                id="newPassword"
                type={showPasswords.new ? 'text' : 'password'}
                value={formData.newPassword}
                onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                className="pr-10"
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => togglePasswordVisibility('new')}
              >
                {showPasswords.new ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
            
            {/* Indicatore Forza Password */}
            {formData.newPassword && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Forza password:</span>
                  <span className={cn(
                    "font-medium",
                    passwordStrength.score < 2 ? "text-red-600" :
                    passwordStrength.score < 4 ? "text-yellow-600" : "text-green-600"
                  )}>
                    {getPasswordStrengthText(passwordStrength.score)}
                  </span>
                </div>
                <Progress 
                  value={(passwordStrength.score / 5) * 100} 
                  className="h-2"
                />
                {passwordStrength.feedback.length > 0 && (
                  <div className="text-sm text-gray-600">
                    <p className="font-medium mb-1">Suggerimenti:</p>
                    <ul className="list-disc list-inside space-y-1">
                      {passwordStrength.feedback.map((suggestion, index) => (
                        <li key={index}>{suggestion}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Conferma Password */}
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Conferma Nuova Password</Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showPasswords.confirm ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                className="pr-10"
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => togglePasswordVisibility('confirm')}
              >
                {showPasswords.confirm ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
            
            {/* Validazione Corrispondenza Password */}
            {formData.confirmPassword && (
              <div className="flex items-center gap-2 text-sm">
                {formData.newPassword === formData.confirmPassword ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-green-600">Le password corrispondono</span>
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 text-red-600" />
                    <span className="text-red-600">Le password non corrispondono</span>
                  </>
                )}
              </div>
            )}
          </div>

          {/* Messaggio di Stato */}
          {message && (
            <Alert className={cn(
              message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
            )}>
              {message.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={cn(
                message.type === 'success' ? 'text-green-800' : 'text-red-800'
              )}>
                {message.text}
              </AlertDescription>
            </Alert>
          )}

          {/* Pulsante Submit */}
          <Button 
            type="submit" 
            className="w-full bg-mariner-600 hover:bg-mariner-700"
            disabled={isLoading || !passwordStrength.isValid || formData.newPassword !== formData.confirmPassword}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Aggiornamento...
              </>
            ) : (
              <>
                <Shield className="h-4 w-4 mr-2" />
                Cambia Password
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

export function PasswordResetForm() {
  const [formData, setFormData] = useState<PasswordResetFormData>({
    email: '',
    console.log({
    email: '',
    userType: 'user'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setMessage(null)

    try {
      if (!formData.email) {
        throw new Error('L\'indirizzo email è obbligatorio')
      }

      const response = await fetch('/api/password/request-password-reset', {
        method: 'POST',
        console.log({
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: formData.email,
          user_type: formData.userType
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        setMessage({
          type: 'success',
          console.log({
          type: 'success',
          text: 'Se l\'email è registrata, riceverai le istruzioni per il reset della password.'
        })
        setFormData({ email: '', userType: 'user' })
      } else {
        throw new Error(result.detail || result.message || 'Errore durante la richiesta di reset')
      }
    } catch (error) {
      setMessage({
        type: 'error',
        console.log({
        type: 'error',
        text: error instanceof Error ? error.message : 'Errore durante la richiesta di reset'
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl flex items-center gap-2">
          <Mail className="h-5 w-5 text-mariner-600" />
          Recupera Password
        </CardTitle>
        <CardDescription>
          Inserisci la tua email per ricevere le istruzioni di reset
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Tipo Utente */}
          <div className="space-y-2">
            <Label>Tipo di Account</Label>
            <div className="flex gap-4">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  value="user"
                  checked={formData.userType === 'user'}
                  onChange={(e) => setFormData(prev => ({ ...prev, userType: e.target.value as 'user' | 'cantiere' }))}
                  className="text-mariner-600"
                />
                <span>Utente</span>
              </label>
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  value="cantiere"
                  checked={formData.userType === 'cantiere'}
                  onChange={(e) => setFormData(prev => ({ ...prev, userType: e.target.value as 'user' | 'cantiere' }))}
                  className="text-mariner-600"
                />
                <span>Cantiere</span>
              </label>
            </div>
          </div>

          {/* Email */}
          <div className="space-y-2">
            <Label htmlFor="email">Indirizzo Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              placeholder="<EMAIL>"
              required
            />
          </div>

          {/* Messaggio di Stato */}
          {message && (
            <Alert className={cn(
              message.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
            )}>
              {message.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-600" />
              )}
              <AlertDescription className={cn(
                message.type === 'success' ? 'text-green-800' : 'text-red-800'
              )}>
                {message.text}
              </AlertDescription>
            </Alert>
          )}

          {/* Pulsante Submit */}
          <Button
            type="submit"
            className="w-full bg-mariner-600 hover:bg-mariner-700"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Invio in corso...
              </>
            ) : (
              <>
                <Mail className="h-4 w-4 mr-2" />
                Invia Link di Reset
              </>
            )}
          </Button>
        </form>

        {/* Informazioni di Sicurezza */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-2">
            <Shield className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Informazioni sulla Sicurezza</p>
              <ul className="space-y-1 text-xs">
                <li>• Il link di reset è valido per 30 minuti</li>
                <li>• Può essere utilizzato una sola volta</li>
                <li>• Se non ricevi l'email, controlla la cartella spam</li>
                <li>• Per motivi di sicurezza, non riveleremo se l'email è registrata</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
