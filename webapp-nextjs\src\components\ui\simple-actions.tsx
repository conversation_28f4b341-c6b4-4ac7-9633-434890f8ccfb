'use client'

import { <PERSON>, Clock, CheckCircle, Trash2 } from 'lucide-react'

interface SimpleActionsProps {
  user: {
    id_utente: number
    }
  }
  onEdit: () => void
  onToggleStatus: () => void
  onDelete: () => void
}

export default function SimpleActions({
  user,
  onEdit,
  onToggleStatus,
  onDelete
}: SimpleActionsProps) {
  
  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onEdit()
  }

  const handleToggleStatus = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onToggleStatus()
  }

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onDelete()
  }

  return (
    <div className="flex items-center gap-1">
      {/* Modifica - sempre disponibile */}
      <button
        onClick={handleEdit}
        type="button"
        className="p-1.5 rounded hover:bg-blue-50 transition-colors"
        title="Modifica utente"
      >
        <Edit className="h-4 w-4 text-blue-600 hover:text-blue-700" />
      </button>

      {/* Abilita/Disabilita */}
      <button
        onClick={handleToggleStatus}
        disabled={user.ruolo === 'owner'}
        type="button"
        className={`p-1.5 rounded transition-colors ${
          user.ruolo === 'owner'
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-slate-50'
        }`}
        title={user.abilitato ? 'Disabilita utente' : 'Abilita utente'}
      >
        {user.abilitato ? (
          <Clock className="h-4 w-4 text-red-500 hover:text-red-600" />
        ) : (
          <CheckCircle className="h-4 w-4 text-green-500 hover:text-green-600" />
        )}
      </button>

      {/* Elimina */}
      <button
        onClick={handleDelete}
        disabled={user.ruolo === 'owner'}
        type="button"
        className={`p-1.5 rounded transition-colors ${
          user.ruolo === 'owner'
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-red-50'
        }`}
        title="Elimina utente"
      >
        <Trash2 className="h-4 w-4 text-red-500 hover:text-red-600" />
      </button>
    </div>
  )
}
