/**
 * SCRIPT PER CORREGGERE CONSOLE.LOG MANCANTI
 * Trova e corregge tutti i console.log incompleti nel codice
 */

const fs = require('fs');
const path = require('path');

class ConsoleLogFixer {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.fixed = 0;
    this.errors = 0;
  }

  log(message, color = 'reset') {
    const colors = {
      green: '\x1b[32m',
      red: '\x1b[31m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m',
      bold: '\x1b[1m'
    };
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
    let files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          // Salta node_modules e .next
          if (!item.startsWith('.') && item !== 'node_modules') {
            files = files.concat(this.findFiles(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      this.log(`❌ Errore lettura directory ${dir}: ${error.message}`, 'red');
    }
    
    return files;
  }

  fixConsoleLogsInFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      let modified = false;
      let newLines = [];
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmed = line.trim();
        
        // Cerca pattern di console.log incompleti
        // Pattern 1: linea che inizia con proprietà di oggetto (es: "cantiereFromAuth: cantiere,")
        if (trimmed.match(/^[a-zA-Z_][a-zA-Z0-9_]*\s*:\s*.+,?\s*$/) && 
            i > 0 && 
            !lines[i-1].includes('console.log') &&
            !lines[i-1].includes('{')) {
          
          // Cerca l'inizio del blocco
          let startIndex = i;
          while (startIndex > 0 && 
                 !lines[startIndex-1].includes('console.log') && 
                 !lines[startIndex-1].trim().endsWith('{')) {
            startIndex--;
            if (lines[startIndex].trim().match(/^[a-zA-Z_][a-zA-Z0-9_]*\s*:\s*.+,?\s*$/)) {
              continue;
            } else {
              break;
            }
          }
          
          // Cerca la fine del blocco
          let endIndex = i;
          while (endIndex < lines.length - 1 && 
                 (lines[endIndex].trim().match(/^[a-zA-Z_][a-zA-Z0-9_]*\s*:\s*.+,?\s*$/) ||
                  lines[endIndex].trim() === '}' ||
                  lines[endIndex].trim() === '})')) {
            endIndex++;
            if (lines[endIndex].trim() === '}' || lines[endIndex].trim() === '})') {
              break;
            }
          }
          
          // Se abbiamo trovato un blocco valido, aggiungi console.log
          if (endIndex > startIndex) {
            const indent = line.match(/^(\s*)/)[1];
            newLines.push(`${indent}console.log({`);
            
            // Aggiungi tutte le linee del blocco
            for (let j = startIndex; j <= endIndex; j++) {
              if (j < lines.length) {
                newLines.push(lines[j]);
              }
            }
            
            // Salta le linee che abbiamo già processato
            i = endIndex;
            modified = true;
            continue;
          }
        }
        
        newLines.push(line);
      }
      
      if (modified) {
        fs.writeFileSync(filePath, newLines.join('\n'), 'utf8');
        this.fixed++;
        this.log(`✅ Corretto: ${path.relative(this.srcDir, filePath)}`, 'green');
        return true;
      }
      
      return false;
    } catch (error) {
      this.log(`❌ Errore elaborazione ${filePath}: ${error.message}`, 'red');
      this.errors++;
      return false;
    }
  }

  run() {
    this.log('\n🔧 Correzione Console.log Mancanti', 'bold');
    this.log('Cerca e corregge console.log incompleti', 'blue');
    
    const files = this.findFiles(this.srcDir);
    this.log(`\n📁 Trovati ${files.length} file da analizzare`, 'blue');
    
    for (const file of files) {
      this.fixConsoleLogsInFile(file);
    }
    
    this.log('\n📊 RIEPILOGO CORREZIONI', 'bold');
    this.log('='.repeat(50), 'blue');
    this.log(`✅ File corretti: ${this.fixed}`, 'green');
    this.log(`❌ Errori: ${this.errors}`, this.errors > 0 ? 'red' : 'green');
    
    if (this.fixed > 0) {
      this.log('\n✅ Correzioni completate!', 'green');
    } else {
      this.log('\n✅ Nessuna correzione necessaria', 'green');
    }
  }
}

// Esecuzione dello script
if (require.main === module) {
  const fixer = new ConsoleLogFixer();
  fixer.run();
}

module.exports = ConsoleLogFixer;
