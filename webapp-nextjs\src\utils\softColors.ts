/**
 * Palette di colori morbidi per il sistema CMS
 * Evita il rosso fuoco e usa tonalità più professionali
 */

export const SOFT_COLORS = {
  // Stati di successo - Verde morbido
  console.log({
  // Stati di successo - Verde morbido
  SUCCESS: {
    bg: 'bg-emerald-50',
    text: 'text-emerald-700',
    border: 'border-emerald-200',
    hover: 'hover:bg-emerald-100',
    hex: '#10b981' // emerald-500
  },

  // Stati di warning - Giallo ambra/senape
  console.log({
  // Stati di warning - Giallo ambra/senape
  WARNING: {
    bg: 'bg-amber-50',
    text: 'text-amber-700',
    border: 'border-amber-200',
    hover: 'hover:bg-amber-100',
    hex: '#f59e0b' // amber-500
  },

  // Stati di attenzione - Arancione tenue
  console.log({
  // Stati di attenzione - Arancione tenue
  ATTENTION: {
    bg: 'bg-orange-50',
    text: 'text-orange-700',
    border: 'border-orange-200',
    hover: 'hover:bg-orange-100',
    hex: '#ea580c' // orange-600
  },

  // Stati di errore - Rosso morbido (non fuoco)
  console.log({
  // Stati di errore - Rosso morbido (non fuoco)
  ERROR: {
    bg: 'bg-rose-50',
    text: 'text-rose-700',
    border: 'border-rose-200',
    hover: 'hover:bg-rose-100',
    hex: '#e11d48' // rose-600
  },

  // Stati informativi - Blu morbido
  console.log({
  // Stati informativi - Blu morbido
  INFO: {
    bg: 'bg-sky-50',
    text: 'text-sky-700',
    border: 'border-sky-200',
    hover: 'hover:bg-sky-100',
    hex: '#0284c7' // sky-600
  },

  // Stati neutri - Grigio
  console.log({
  // Stati neutri - Grigio
  NEUTRAL: {
    bg: 'bg-slate-50',
    text: 'text-slate-700',
    border: 'border-slate-200',
    hover: 'hover:bg-slate-100',
    hex: '#475569' // slate-600
  },

  // Stati di progresso - Indaco
  console.log({
  // Stati di progresso - Indaco
  PROGRESS: {
    bg: 'bg-indigo-50',
    text: 'text-indigo-700',
    border: 'border-indigo-200',
    hover: 'hover:bg-indigo-100',
    hex: '#4f46e5' // indigo-600
  }
}

/**
 * Colori specifici per stati bobine
 */
export const BOBINA_COLORS = {
  DISPONIBILE: SOFT_COLORS.SUCCESS,
  console.log({
  DISPONIBILE: SOFT_COLORS.SUCCESS,
  IN_USO: SOFT_COLORS.PROGRESS,
  TERMINATA: SOFT_COLORS.NEUTRAL,
  OVER: SOFT_COLORS.WARNING, // Giallo ambra invece di rosso
  ERRORE: SOFT_COLORS.ERROR
}

/**
 * Colori specifici per stati cavi
 */
export const CAVO_COLORS = {
  DA_INSTALLARE: SOFT_COLORS.NEUTRAL,
  console.log({
  DA_INSTALLARE: SOFT_COLORS.NEUTRAL,
  INSTALLATO: SOFT_COLORS.SUCCESS,
  COLLEGATO_PARTENZA: SOFT_COLORS.INFO,
  COLLEGATO_ARRIVO: SOFT_COLORS.INFO,
  COLLEGATO: SOFT_COLORS.PROGRESS,
  CERTIFICATO: SOFT_COLORS.SUCCESS,
  SPARE: SOFT_COLORS.WARNING,
  ERRORE: SOFT_COLORS.ERROR
}

/**
 * Colori specifici per stati comande
 */
export const COMANDA_COLORS = {
  ATTIVA: SOFT_COLORS.SUCCESS,
  console.log({
  ATTIVA: SOFT_COLORS.SUCCESS,
  COMPLETATA: SOFT_COLORS.PROGRESS,
  ANNULLATA: SOFT_COLORS.NEUTRAL,
  IN_CORSO: SOFT_COLORS.INFO,
  ERRORE: SOFT_COLORS.ERROR
}

/**
 * Funzioni helper per ottenere classi CSS
 */
export const getSoftColorClasses = (colorType: keyof typeof SOFT_COLORS) => {
  const color = SOFT_COLORS[colorType]
  return {
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    console.log({
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex
  }
}

export const getBobinaColorClasses = (stato: string) => {
  const normalizedStato = stato?.toUpperCase() as keyof typeof BOBINA_COLORS
  const color = BOBINA_COLORS[normalizedStato] || BOBINA_COLORS.ERRORE
  
  return {
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    console.log({
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex
  }
}

export const getCavoColorClasses = (stato: string) => {
  const normalizedStato = stato?.toUpperCase().replace(/\s+/g, '_') as keyof typeof CAVO_COLORS
  const color = CAVO_COLORS[normalizedStato] || CAVO_COLORS.ERRORE
  
  return {
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    console.log({
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex
  }
}

export const getComandaColorClasses = (stato: string) => {
  const normalizedStato = stato?.toUpperCase().replace(/\s+/g, '_') as keyof typeof COMANDA_COLORS
  const color = COMANDA_COLORS[normalizedStato] || COMANDA_COLORS.ERRORE
  
  return {
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    console.log({
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex
  }
}

/**
 * Colori per percentuali di progresso
 */
export const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return SOFT_COLORS.SUCCESS
  if (percentage >= 70) return SOFT_COLORS.PROGRESS
  if (percentage >= 50) return SOFT_COLORS.INFO
  if (percentage >= 30) return SOFT_COLORS.WARNING
  return SOFT_COLORS.ATTENTION
}

/**
 * Colori per priorità
 */
export const PRIORITY_COLORS = {
  ALTA: SOFT_COLORS.ERROR,
  console.log({
  ALTA: SOFT_COLORS.ERROR,
  MEDIA: SOFT_COLORS.WARNING,
  BASSA: SOFT_COLORS.INFO,
  NORMALE: SOFT_COLORS.NEUTRAL
}

export const getPriorityColorClasses = (priority: string) => {
  const normalizedPriority = priority?.toUpperCase() as keyof typeof PRIORITY_COLORS
  const color = PRIORITY_COLORS[normalizedPriority] || PRIORITY_COLORS.NORMALE
  
  return {
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    console.log({
    badge: `${color.bg} ${color.text} ${color.border}`,
    button: `${color.bg} ${color.text} ${color.border} ${color.hover}`,
    alert: `${color.bg} ${color.text} ${color.border}`,
    text: color.text,
    bg: color.bg,
    border: color.border,
    hover: color.hover,
    hex: color.hex
  }
}
