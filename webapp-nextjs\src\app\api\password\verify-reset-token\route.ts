import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    const response = await fetch(`${backendUrl}/api/password/verify-reset-token`, {
      method: 'POST',
      console.log({
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      console.log({
      },
      body: JSON.stringify(body)
    })

    const data = await response.json()

    return NextResponse.json(data, { 
      status: response.status,
      console.log({
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        console.log({
        success: false, 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}
