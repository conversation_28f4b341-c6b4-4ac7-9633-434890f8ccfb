'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { PrimaryButton } from '@/components/ui/animated-button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Eye, EyeOff, Shield, AlertTriangle } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { validateUsername, validatePassword, checkRateLimit, generateCSRFToken } from '@/utils/securityValidation'

interface LoginAttempt {
  timestamp: number
  console.log({
  timestamp: number
  ip: string
  success: boolean
}

export default function SecureLoginForm() {
  const router = useRouter()
  const { login } = useAuth()
  
  const [formData, setFormData] = useState({
    username: '',
    console.log({
    username: '',
    password: ''
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [csrfToken, setCsrfToken] = useState('')
  const [loginAttempts, setLoginAttempts] = useState<LoginAttempt[]>([])
  const [isBlocked, setIsBlocked] = useState(false)
  const [blockTimeRemaining, setBlockTimeRemaining] = useState(0)

  // Genera CSRF token al mount
  useEffect(() => {
    setCsrfToken(generateCSRFToken())
  }, [])

  // Timer per sblocco automatico
  useEffect(() => {
    if (blockTimeRemaining > 0) {
      const timer = setTimeout(() => {
        setBlockTimeRemaining(prev => prev - 1)
        if (blockTimeRemaining === 1) {
          setIsBlocked(false)
          setError('')
        }
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [blockTimeRemaining])

  // Gestisce il cambio dei valori del form
  const handleChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Rimuovi l'errore per questo campo se presente
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  // Validazione in tempo reale
  const validateField = (name: string, value: string) => {
    switch (name) {
      case 'username':
        const usernameValidation = validateUsername(value)
        if (!usernameValidation.isValid) {
          setErrors(prev => ({ ...prev, username: usernameValidation.error! }))
        }
        break
      case 'password':
        if (value.length > 0 && value.length < 3) {
          setErrors(prev => ({ ...prev, password: 'Password troppo corta' }))
        }
        break
    }
  }

  // Validazione completa del form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // Valida username
    const usernameValidation = validateUsername(formData.username)
    if (!usernameValidation.isValid) {
      newErrors.username = usernameValidation.error!
    }

    // Valida password (controlli base per login)
    if (!formData.password) {
      newErrors.password = 'Password è obbligatoria'
    } else if (formData.password.length < 3) {
      newErrors.password = 'Password troppo corta'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Controlla se l'utente è bloccato per troppi tentativi
  const checkLoginAttempts = (): boolean => {
    const now = Date.now()
    const recentAttempts = loginAttempts.filter(attempt => 
      now - attempt.timestamp < 15 * 60 * 1000 // Ultimi 15 minuti
    )

    const failedAttempts = recentAttempts.filter(attempt => !attempt.success)
    
    if (failedAttempts.length >= 5) {
      setIsBlocked(true)
      setBlockTimeRemaining(300) // 5 minuti di blocco
      setError('Troppi tentativi di login falliti. Account temporaneamente bloccato.')
      return false
    }

    return true
  }

  // Registra tentativo di login
  const recordLoginAttempt = (success: boolean) => {
    const attempt: LoginAttempt = {
      timestamp: Date.now(),
      console.log({
      timestamp: Date.now(),
      ip: 'client', // In produzione usare IP reale
      success
    }
    
    setLoginAttempts(prev => [...prev.slice(-9), attempt]) // Mantieni solo ultimi 10
  }

  // Gestisce il submit del form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Controllo blocco
    if (isBlocked) {
      setError(`Account bloccato. Riprova tra ${blockTimeRemaining} secondi.`)
      return
    }

    // Rate limiting globale
    if (!checkRateLimit('login-global', 10, 60000)) { // Max 10 login per minuto globali
      setError('Troppi tentativi di login. Riprova tra un minuto.')
      return
    }

    // Rate limiting per username
    const userRateKey = `login-user-${formData.username}`
    if (!checkRateLimit(userRateKey, 3, 300000)) { // Max 3 tentativi per 5 minuti per utente
      setError('Troppi tentativi per questo username. Riprova tra 5 minuti.')
      return
    }

    // Controllo tentativi precedenti
    if (!checkLoginAttempts()) {
      return
    }

    if (!validateForm()) {
      recordLoginAttempt(false)
      return
    }

    setLoading(true)
    setError('')

    try {
      // Aggiungi CSRF token ai dati
      const loginData = {
        ...formData,
        csrfToken
      }

      const result = await login(loginData.username, loginData.password)
      
      if (result.success) {
        recordLoginAttempt(true)
        
        // Reindirizza in base al ruolo
        if (result.user?.ruolo === 'owner') {
          router.push('/admin')
        } else if (result.user?.ruolo === 'user') {
          router.push('/cantieri')
        } else if (result.user?.ruolo === 'cantieri_user') {
          router.push('/cavi')
        } else {
          router.push('/')
        }
      } else {
        recordLoginAttempt(false)
        setError(result.error || 'Credenziali non valide')
      }
    } catch (err: any) {
      recordLoginAttempt(false)
      setError('Errore durante il login. Riprova.')
    } finally {
      setLoading(false)
      // Rigenera CSRF token dopo ogni tentativo
      setCsrfToken(generateCSRFToken())
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-6">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center mb-4">
            <Shield className="h-8 w-8 text-blue-600 mr-2" />
            <CardTitle className="text-2xl font-bold text-slate-900">Login Sicuro</CardTitle>
          </div>
          <p className="text-slate-600">Accedi al sistema CABLYS</p>
        </CardHeader>
        <CardContent>
          {/* Avviso sicurezza */}
          <Alert className="mb-6 border-blue-200 bg-blue-50">
            <Shield className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              Sistema protetto con validazione avanzata e rate limiting
            </AlertDescription>
          </Alert>

          {/* Avviso blocco */}
          {isBlocked && (
            <Alert className="mb-6 border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                Account temporaneamente bloccato per sicurezza. Tempo rimanente: {blockTimeRemaining}s
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert className="mb-6 border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">{error}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* CSRF Token nascosto */}
            <input type="hidden" name="csrfToken" value={csrfToken} />

            {/* Username */}
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                type="text"
                value={formData.username}
                onChange={(e) => handleChange('username', e.target.value)}
                onBlur={(e) => validateField('username', e.target.value)}
                disabled={loading || isBlocked}
                className={errors.username ? 'border-red-500' : ''}
                autoComplete="username"
                maxLength={20}
              />
              {errors.username && <p className="text-sm text-red-600">{errors.username}</p>}
            </div>

            {/* Password */}
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => handleChange('password', e.target.value)}
                  onBlur={(e) => validateField('password', e.target.value)}
                  disabled={loading || isBlocked}
                  className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                  autoComplete="current-password"
                  maxLength={128}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                  disabled={loading || isBlocked}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              {errors.password && <p className="text-sm text-red-600">{errors.password}</p>}
            </div>

            {/* Submit Button */}
            <PrimaryButton
              type="submit"
              loading={loading}
              disabled={isBlocked}
              className="w-full"
              glow
            >
              {loading ? 'Accesso in corso...' : 'Accedi'}
            </PrimaryButton>
          </form>

          {/* Info sicurezza */}
          <div className="mt-6 text-center text-xs text-slate-500">
            <p>Tentativi falliti: {loginAttempts.filter(a => !a.success).length}/5</p>
            <p>Sistema protetto con crittografia e monitoraggio</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
