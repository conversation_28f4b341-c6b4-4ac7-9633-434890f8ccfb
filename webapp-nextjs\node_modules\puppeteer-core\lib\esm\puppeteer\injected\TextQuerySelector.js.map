{"version": 3, "file": "TextQuerySelector.js", "sourceRoot": "", "sources": ["../../../../src/injected/TextQuerySelector.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,iBAAiB,EACjB,6BAA6B,GAC9B,MAAM,kBAAkB,CAAC;AAE1B;;;;GAIG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG,QAAQ,CAAC,EAC3C,IAAU,EACV,QAAgB;IAEhB,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,IAAI,IAAI,YAAY,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,OAAoC,CAAC;YACzC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACrB,OAAO,GAAG,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,oBAAoB,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC5D,CAAC;YACD,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;gBAC5B,MAAM,KAAK,CAAC;gBACZ,OAAO,GAAG,IAAI,CAAC;YACjB,CAAC;QACH,CAAC;IACH,CAAC;IACD,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO;IACT,CAAC;IAED,IAAI,IAAI,YAAY,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC;QACnE,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,MAAM,IAAI,CAAC;QACb,CAAC;IACH,CAAC;AACH,CAAC,CAAC"}