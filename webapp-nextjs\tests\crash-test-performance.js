/**
 * TEST OTTIMIZZAZIONI PERFORMANCE
 * Analizza e implementa ottimizzazioni React per migliorare le performance
 */

const fs = require('fs');
const path = require('path');

class PerformanceOptimizer {
  constructor() {
    this.srcDir = path.join(__dirname, '..', 'src');
    this.results = {
      debouncing: { found: 0, optimized: 0, files: [] },
      useMemo: { found: 0, optimized: 0, files: [] },
      lazyLoading: { found: 0, optimized: 0, files: [] },
      reRenders: { found: 0, optimized: 0, files: [] },
      largeComponents: { found: 0, optimized: 0, files: [] }
    };
  }

  log(message, color = 'reset') {
    const colors = {
      green: '\x1b[32m',
      red: '\x1b[31m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m',
      bold: '\x1b[1m'
    };
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  findFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
    let files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (!item.startsWith('.') && item !== 'node_modules') {
            files = files.concat(this.findFiles(fullPath, extensions));
          }
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      this.log(`❌ Errore lettura directory ${dir}: ${error.message}`, 'red');
    }
    
    return files;
  }

  analyzeDebouncing(filePath, content) {
    const issues = [];
    
    // Cerca input con onChange senza debouncing
    const inputPatterns = [
      /onChange=\{[^}]*\}/g,
      /onInput=\{[^}]*\}/g,
      /onKeyUp=\{[^}]*\}/g
    ];
    
    inputPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          if (!content.includes('debounce') && !content.includes('setTimeout')) {
            issues.push({
              type: 'debouncing',
              issue: 'Input senza debouncing',
              suggestion: 'Implementare debouncing per input di ricerca',
              code: match
            });
          }
        });
      }
    });
    
    return issues;
  }

  analyzeUseMemo(filePath, content) {
    const issues = [];
    
    // Cerca calcoli costosi senza useMemo
    const expensivePatterns = [
      /\.filter\([^)]+\)\.map\([^)]+\)/g,
      /\.sort\([^)]+\)/g,
      /\.reduce\([^)]+\)/g
    ];
    
    expensivePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches && !content.includes('useMemo')) {
        matches.forEach(match => {
          issues.push({
            type: 'useMemo',
            issue: 'Calcolo costoso senza memoization',
            suggestion: 'Usare useMemo per operazioni costose',
            code: match
          });
        });
      }
    });
    
    return issues;
  }

  analyzeLazyLoading(filePath, content) {
    const issues = [];
    
    // Cerca import di componenti grandi senza lazy loading
    const importPattern = /import\s+\{?[^}]*\}?\s+from\s+['"][^'"]*['"];?/g;
    const matches = content.match(importPattern);
    
    if (matches) {
      matches.forEach(match => {
        if (match.includes('Dialog') || match.includes('Modal') || match.includes('Chart')) {
          if (!content.includes('lazy') && !content.includes('dynamic')) {
            issues.push({
              type: 'lazyLoading',
              issue: 'Componente pesante senza lazy loading',
              suggestion: 'Implementare lazy loading per componenti pesanti',
              code: match
            });
          }
        }
      });
    }
    
    return issues;
  }

  analyzeReRenders(filePath, content) {
    const issues = [];
    
    // Cerca oggetti/array creati inline
    const inlinePatterns = [
      /\{\s*[^}]+\s*\}/g,
      /\[\s*[^\]]+\s*\]/g
    ];
    
    inlinePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          if (match.length > 20 && content.includes('props') && !content.includes('useMemo')) {
            issues.push({
              type: 'reRenders',
              issue: 'Oggetto/array inline che causa re-render',
              suggestion: 'Spostare oggetti/array fuori dal render o usare useMemo',
              code: match.substring(0, 50) + '...'
            });
          }
        });
      }
    });
    
    return issues;
  }

  analyzeLargeComponents(filePath, content) {
    const issues = [];
    const lines = content.split('\n').length;
    
    if (lines > 300) {
      issues.push({
        type: 'largeComponents',
        issue: `Componente troppo grande (${lines} linee)`,
        suggestion: 'Dividere in componenti più piccoli',
        code: `File: ${path.basename(filePath)}`
      });
    }
    
    return issues;
  }

  analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const allIssues = [];
      
      // Analizza diversi aspetti delle performance
      allIssues.push(...this.analyzeDebouncing(filePath, content));
      allIssues.push(...this.analyzeUseMemo(filePath, content));
      allIssues.push(...this.analyzeLazyLoading(filePath, content));
      allIssues.push(...this.analyzeReRenders(filePath, content));
      allIssues.push(...this.analyzeLargeComponents(filePath, content));
      
      // Raggruppa per tipo
      allIssues.forEach(issue => {
        this.results[issue.type].found++;
        this.results[issue.type].files.push({
          file: path.relative(this.srcDir, filePath),
          issue: issue.issue,
          suggestion: issue.suggestion,
          code: issue.code
        });
      });
      
      return allIssues;
    } catch (error) {
      this.log(`❌ Errore analisi ${filePath}: ${error.message}`, 'red');
      return [];
    }
  }

  generateOptimizationSuggestions() {
    this.log('\n💡 SUGGERIMENTI DI OTTIMIZZAZIONE', 'bold');
    this.log('='.repeat(60), 'blue');
    
    // Debouncing
    if (this.results.debouncing.found > 0) {
      this.log('\n🔍 DEBOUNCING', 'yellow');
      this.log(`Trovati ${this.results.debouncing.found} input senza debouncing`, 'yellow');
      this.log('Suggerimento: Implementare hook useDebounce per input di ricerca', 'blue');
      this.log('Esempio:', 'blue');
      this.log(`
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebouncedValue(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debouncedValue;
};`, 'green');
    }
    
    // UseMemo
    if (this.results.useMemo.found > 0) {
      this.log('\n🧠 MEMOIZATION', 'yellow');
      this.log(`Trovati ${this.results.useMemo.found} calcoli costosi senza memoization`, 'yellow');
      this.log('Suggerimento: Usare useMemo per operazioni costose', 'blue');
      this.log('Esempio:', 'blue');
      this.log(`
const filteredData = useMemo(() => {
  return data.filter(item => item.active).sort((a, b) => a.name.localeCompare(b.name));
}, [data]);`, 'green');
    }
    
    // Lazy Loading
    if (this.results.lazyLoading.found > 0) {
      this.log('\n⚡ LAZY LOADING', 'yellow');
      this.log(`Trovati ${this.results.lazyLoading.found} componenti pesanti senza lazy loading`, 'yellow');
      this.log('Suggerimento: Implementare lazy loading per componenti pesanti', 'blue');
      this.log('Esempio:', 'blue');
      this.log(`
const HeavyDialog = lazy(() => import('./HeavyDialog'));

// Nel componente:
<Suspense fallback={<div>Caricamento...</div>}>
  {showDialog && <HeavyDialog />}
</Suspense>`, 'green');
    }
    
    // Re-renders
    if (this.results.reRenders.found > 0) {
      this.log('\n🔄 RE-RENDERS', 'yellow');
      this.log(`Trovati ${this.results.reRenders.found} potenziali cause di re-render`, 'yellow');
      this.log('Suggerimento: Evitare oggetti/array inline nelle props', 'blue');
      this.log('Esempio:', 'blue');
      this.log(`
// ❌ Cattivo
<Component style={{margin: 10}} data={[1, 2, 3]} />

// ✅ Buono
const style = {margin: 10};
const data = [1, 2, 3];
<Component style={style} data={data} />`, 'green');
    }
    
    // Large Components
    if (this.results.largeComponents.found > 0) {
      this.log('\n📦 COMPONENTI GRANDI', 'yellow');
      this.log(`Trovati ${this.results.largeComponents.found} componenti troppo grandi`, 'yellow');
      this.log('Suggerimento: Dividere in componenti più piccoli e riutilizzabili', 'blue');
    }
  }

  printDetailedResults() {
    this.log('\n📋 DETTAGLI PROBLEMI TROVATI', 'bold');
    this.log('='.repeat(60), 'blue');
    
    Object.entries(this.results).forEach(([category, data]) => {
      if (data.found > 0) {
        this.log(`\n${category.toUpperCase()}:`, 'yellow');
        data.files.slice(0, 5).forEach(item => { // Mostra solo i primi 5
          this.log(`  📁 ${item.file}`, 'blue');
          this.log(`     ⚠️  ${item.issue}`, 'yellow');
          this.log(`     💡 ${item.suggestion}`, 'green');
          if (item.code) {
            this.log(`     📝 ${item.code}`, 'reset');
          }
        });
        if (data.files.length > 5) {
          this.log(`     ... e altri ${data.files.length - 5} file`, 'blue');
        }
      }
    });
  }

  printSummary() {
    this.log('\n📊 RIEPILOGO ANALISI PERFORMANCE', 'bold');
    this.log('='.repeat(60), 'blue');
    
    let totalIssues = 0;
    Object.entries(this.results).forEach(([category, data]) => {
      const categoryName = category.charAt(0).toUpperCase() + category.slice(1);
      this.log(`\n${categoryName}: ${data.found} problemi trovati`, 
        data.found > 0 ? 'yellow' : 'green');
      totalIssues += data.found;
    });
    
    this.log(`\n🎯 TOTALE PROBLEMI: ${totalIssues}`, 
      totalIssues > 20 ? 'red' : totalIssues > 10 ? 'yellow' : 'green');
    
    if (totalIssues === 0) {
      this.log('\n✅ Nessun problema di performance critico trovato!', 'green');
    } else if (totalIssues <= 10) {
      this.log('\n⚠️  Alcuni problemi di performance da ottimizzare', 'yellow');
    } else {
      this.log('\n❌ Molti problemi di performance da risolvere', 'red');
    }
  }

  run() {
    this.log('\n⚡ Test Ottimizzazioni Performance', 'bold');
    this.log('Analizza codice per identificare ottimizzazioni React', 'blue');
    
    const files = this.findFiles(this.srcDir);
    this.log(`\n📁 Analizzando ${files.length} file...`, 'blue');
    
    files.forEach(file => {
      this.analyzeFile(file);
    });
    
    this.printSummary();
    this.generateOptimizationSuggestions();
    this.printDetailedResults();
  }
}

// Esecuzione dei test
if (require.main === module) {
  const optimizer = new PerformanceOptimizer();
  optimizer.run();
}

module.exports = PerformanceOptimizer;
