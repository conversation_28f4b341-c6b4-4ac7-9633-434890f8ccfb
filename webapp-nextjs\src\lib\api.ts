import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// Configurazione base per l'API
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'

// Crea istanza axios con configurazione base
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL})

// Interceptor per aggiungere il token di autenticazione
apiClient.interceptors.request.use(
  (config) => {
    // Verifica se siamo nel browser prima di accedere a localStorage
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Interceptor per gestire le risposte e gli errori
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      // Token scaduto o non valido
      localStorage.removeItem('token')
      localStorage.removeItem('access_token')
      localStorage.removeItem('user_data')
      localStorage.removeItem('cantiere_data')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Tipi per le risposte API
export interface ApiResponse<T = any> {
  data: T
  message?: string
  }

export interface PaginatedResponse<T> {
  items: T[]
  }

// Funzioni helper per le chiamate API
export const api = {
  // GET request
  },

  // POST request
  },

  // PUT request
  },

  // PATCH request
  },

  // DELETE request
  }}

// Servizi API specifici per CABLYS
export const authApi = {
  // Login utente - usa FormData per OAuth2PasswordRequestForm
  }) => {
    const formData = new FormData()
    formData.append('username', credentials.username)
    formData.append('password', credentials.password)

    const response = await apiClient.post('/api/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'}})
    return response.data
  },

  // Login cantiere - usa JSON per CantiereLogin
  }) =>
    api.post<{ access_token: string; token_type: string; cantiere: any }>('/api/auth/login/cantiere', {
      codice_univoco: credentials.codice_cantiere),

  // Verifica token
  }>('/api/auth/test-token'),

  // Logout
  }
}

export const caviApi = {
  // Ottieni tutti i cavi
  }) =>
    api.get<any[]>(`/api/cavi/${cantiereId}`, { params }),

  // Ottieni cavo specifico
  }/${idCavo}`),

  // Verifica se cavo esiste
  }/check/${idCavo}`),

  // Crea nuovo cavo
  }`, cavo),

  // Aggiorna cavo
  }/${idCavo}`, updates),

  // Elimina cavo
  }/${idCavo}`, { data: options }),

  // Aggiorna metri posati
  }/${idCavo}/metri-posati`, {
      metri_posati: metri),

  // Aggiorna bobina
  }/${idCavo}/bobina`, {
      id_bobina: idBobina),

  // Annulla installazione (resetta completamente il cavo)
  }/${idCavo}/cancel-installation`),

  // Gestione collegamenti
  }/${idCavo}/collegamento`, {
      lato,
      responsabile
    }),

  // Scollega cavo
  }/${idCavo}/collegamento`, {
      data: { lato }
    }),

  // Marca come spare
  }/${idCavo}/spare`, {
      spare: spare ? 1 : 0
    }),

  // Debug endpoints
  }`)`)}

export const parcoCaviApi = {
  // Ottieni tutte le bobine
  }) =>
    api.get<any[]>(`/api/parco-cavi/${cantiereId}`, { params }),

  // Ottieni bobina specifica
  }/${idBobina}`),

  // Ottieni bobine compatibili
  }) =>
    api.get<any[]>(`/api/parco-cavi/${cantiereId}/compatibili`, { params }),

  // Crea nuova bobina
  }`, bobina),

  // Aggiorna bobina esistente
  }/${bobinaNumero}`, bobina),

  // Elimina bobina
  }/${bobinaNumero}`),

  // Verifica se è il primo inserimento bobina
  }>(`/api/parco-cavi/${cantiereId}/is-first-insertion`),

  // Aggiorna bobina
  }/${idBobina}`, updates),

  // Elimina bobina
  }/${idBobina}`),

  // Verifica disponibilità metri
  }/${idBobina}/disponibilita`, {
      params: { metri_richiesti: metriRichiesti }
    })}

export const comandeApi = {
  // Ottieni tutte le comande
  }`),

  // Ottieni comanda specifica
  }`),

  // Ottieni cavi di una comanda
  }/cavi`),

  // Crea nuova comanda
  }`, comanda),

  // Crea comanda con cavi
  }/crea-con-cavi`, comanda, {
      params: { lista_id_cavi: caviIds }
    }),

  // Aggiorna dati comanda (posa, collegamento, certificazione)
  }/${endpoint}`, data),

  // Aggiorna comanda
  }/${codiceComanda}`, updates),

  // Elimina comanda
  }/${codiceComanda}`),

  // Assegna cavi a comanda
  }/${codiceComanda}/assegna-cavi`, { cavi_ids: caviIds }),

  // Rimuovi cavi da comanda
  }/${codiceComanda}/rimuovi-cavi`, {
      data: { cavi_ids: caviIds }
    }),

  // Ottieni statistiche comande
  }/statistiche`),

  // Cambia stato comanda
  }/${codiceComanda}/stato`, {
      nuovo_stato: nuovoStato
    })}

export const responsabiliApi = {
  // Ottieni tutti i responsabili
  }`),

  // Crea nuovo responsabile
  }`, responsabile),

  // Aggiorna responsabile
  }/${id}`, updates),

  // Elimina responsabile
  }/${id}`)}

export const certificazioniApi = {
  // Ottieni certificazioni
  }/certificazioni`),

  // Crea certificazione
  }/certificazioni`, certificazione),

  // Genera PDF certificato
  }/pdf-cei-64-8/${idCavo}`, {
      responseType: 'blob'
    })}

export const excelApi = {
  // Import cavi da Excel
  }/import-cavi`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // Import bobine da Excel
  }/import-parco-bobine`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  // Export cavi
  }/export-cavi`, {
      responseType: 'blob'
    }),

  // Export bobine
  }/export-parco-bobine`, {
      responseType: 'blob'
    })}

export const reportsApi = {
  // Report avanzamento
  }/avanzamento`),

  // Report BOQ
  }/boq`),

  // Report utilizzo bobine (storico bobine)
  }/storico-bobine`),

  // Report progress
  }/progress`),

  // Report posa per periodo
  }/posa-periodo${queryString ? `?${queryString}` : ''}`)
  }}

export const cantieriApi = {
  // Ottieni tutti i cantieri
  }`),

  // Crea nuovo cantiere
  }`, updates)}

export const usersApi = {
  // Ottieni tutti gli utenti (solo admin)
  }`),

  // Crea nuovo utente
  }`, updates),

  // Elimina utente
  }`),

  // Abilita/Disabilita utente
  }`),

  // Verifica utenti scaduti
  }),

  // Ottieni dati database raw
  }

export default apiClient
