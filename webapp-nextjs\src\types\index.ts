// Tipi per l'autenticazione
export interface User {
  id_utente: number
  }

export interface Cantiere {
  id_cantiere: number
  }

// Tipi per i cavi
export interface Cavo {
  id_cavo: string
  }

// Tipi per il parco cavi (bobine)
export interface ParcoCavo {
  id_bobina: string
  }

// Tipi per le comande
export interface Comanda {
  codice_comanda: string
  }

export interface ComandaDettaglio {
  id_dettaglio: number
  }

// Tipi per i responsabili
export interface Responsabile {
  id_responsabile: number
  }

// Tipi per le certificazioni
export interface CertificazioneCavo {
  id_certificazione: number
  }

export interface StrumentoCertificato {
  id_strumento: number
  }

// Tipi per i report
export interface ReportAvanzamento {
  totale_cavi: number
  }

export interface ReportBOQ {
  tipologie: Array<{
    tipologia: string
    }>
}

// Tipi per le tipologie cavi
export interface CategoriaCavo {
  id_categoria: number
  }

export interface ProduttoreCavo {
  id_produttore: number
  }

export interface StandardCavo {
  id_standard: number
  }

export interface TipologiaCavo {
  id_tipologia: number
  }

// Tipi per i work logs (produttività)
export interface WorkLog {
  id_log: number
  }

// Tipi per le API responses
export interface ApiResponse<T = any> {
  data: T
  message?: string
  }

export interface PaginatedResponse<T> {
  items: T[]
  }

// Tipi per i filtri e ricerche
export interface FiltriCavi {
  tipologia?: string
  stato_installazione?: string
  responsabile_posa?: string
  id_bobina?: string
  search?: string
  page?: number
  size?: number
}

export interface FiltriComande {
  tipo_comanda?: string
  stato?: string
  responsabile?: string
  data_da?: string
  data_a?: string
}

// Tipi per le statistiche
export interface StatisticheCantiere {
  totale_cavi: number
  }

// Tipi per i form
export interface CavoFormData {
  id_cavo: string
  }

export interface ComandaFormData {
  tipo_comanda: string
  descrizione?: string
  data_scadenza?: string
  }

// Enum per i valori fissi
export enum StatoInstallazione {
  NON_INSTALLATO = 'non_installato',
  IN_CORSO = 'in_corso',
  INSTALLATO = 'installato'
}

export enum TipoComanda {
  POSA = 'POSA',
  COLLEGAMENTO_PARTENZA = 'COLLEGAMENTO_PARTENZA',
  COLLEGAMENTO_ARRIVO = 'COLLEGAMENTO_ARRIVO',
  CERTIFICAZIONE = 'CERTIFICAZIONE'
}

export enum StatoComanda {
  CREATA = 'CREATA',
  ASSEGNATA = 'ASSEGNATA',
  IN_CORSO = 'IN_CORSO',
  COMPLETATA = 'COMPLETATA',
  ANNULLATA = 'ANNULLATA'
}

export enum RuoloUtente {
  OWNER = 'owner',
  USER = 'user',
  CANTIERI_USER = 'cantieri_user'
}
