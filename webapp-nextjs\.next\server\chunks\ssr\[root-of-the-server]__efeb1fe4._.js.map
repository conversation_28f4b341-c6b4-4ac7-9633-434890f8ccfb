{"version": 3, "sources": [], "sections": [{"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton, QuickButton } from '@/components/ui/animated-button'\nimport { Badge } from '@/components/ui/badge'\nimport { useAuth } from '@/contexts/AuthContext'\nimport {\n  Cable,\n  Home,\n  Activity,\n  BarChart3,\n  Settings,\n  Users,\n  Menu,\n  X,\n  Building2,\n  ClipboardList,\n  FileText,\n  LogOut,\n  Package,\n  ChevronDown,\n  Upload,\n  Download\n} from 'lucide-react'\n\nconst getNavigation = (userRole: string | undefined, isImpersonating: boolean, impersonatedUser: any, cantiereId?: number) => {\n  // Home button - testo personalizzato come nella webapp originale\n  const homeButton = {\n    name: userRole === 'owner' ? \"Menu Admin\" :\n          userRole === 'user' ? \"Lista Cantieri\" :\n          userRole === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\",\n    href: userRole === 'owner' ? '/admin' :\n          userRole === 'user' ? '/cantieri' :\n          userRole === 'cantieri_user' ? '/cavi' : '/',\n    icon: Home\n  }\n\n  if (userRole === 'owner' && !isImpersonating) {\n    // Solo amministratore - solo il pulsante Home che va al pannello admin\n    return [homeButton]\n  }\n\n  if (userRole === 'user' || (isImpersonating && impersonatedUser?.role === 'user')) {\n    // Utente standard - Home + eventualmente cantieri se impersonificato\n    const nav = [homeButton]\n    if (isImpersonating) {\n      nav.push({ name: 'Cantieri', href: '/cantieri', icon: Building2 })\n    }\n\n    // Se un cantiere è selezionato, aggiungi i menu di gestione come nella webapp originale\n    if (cantiereId) {\n      nav.push(\n        { name: 'Visualizza Cavi', href: '/cavi', icon: Cable },\n        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },\n        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },\n        { name: 'Report', href: '/reports', icon: BarChart3 },\n        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },\n        { name: '⚡ Produttività', href: '/productivity', icon: Activity },\n      )\n    }\n\n    return nav\n  }\n\n  if (userRole === 'cantieri_user' || (isImpersonating && impersonatedUser?.role === 'cantieri_user')) {\n    // Utente cantiere - menu completo come nella webapp originale\n    const nav = [homeButton]\n\n    // Se un cantiere è selezionato, aggiungi i menu di gestione\n    if (cantiereId) {\n      // Se non è cantieri_user diretto, aggiungi Visualizza Cavi\n      if (userRole !== 'cantieri_user') {\n        nav.push({ name: 'Visualizza Cavi', href: '/cavi', icon: Cable })\n      }\n\n      nav.push(\n        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },\n        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },\n        { name: 'Report', href: '/reports', icon: BarChart3 },\n        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },\n        { name: '⚡ Produttività', href: '/productivity', icon: Activity },\n      )\n    }\n\n    return nav\n  }\n\n  // Default\n  return [homeButton]\n}\n\nexport function Navbar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [excelDropdownOpen, setExcelDropdownOpen] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const pathname = usePathname()\n  const { user, cantiere, isAuthenticated, isImpersonating, impersonatedUser, logout } = useAuth()\n\n  // Recupera l'ID del cantiere selezionato dal localStorage o dal context\n  const cantiereId = cantiere?.id_cantiere || (typeof window !== 'undefined' ? parseInt(localStorage.getItem('selectedCantiereId') || '0') : 0)\n  const cantiereName = cantiere?.commessa || (typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') : '') || `Cantiere ${cantiereId}`\n\n  const navigation = getNavigation(user?.ruolo, isImpersonating, impersonatedUser, cantiereId)\n\n  // Chiudi dropdown quando si clicca fuori\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setExcelDropdownOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  // Non mostrare navbar nella pagina di login\n  if (pathname === '/login') {\n    return null\n  }\n\n  // Se non autenticato, non mostrare navbar\n  if (!isAuthenticated) {\n    return null\n  }\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm\">\n      <div className=\"max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n\n          {/* Tutto a sinistra: Logo + Navigation */}\n          <div className=\"flex items-center space-x-6\">\n            {/* Logo e Brand */}\n            <div className=\"flex items-center space-x-3 cursor-default\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\">\n                <Cable className=\"w-5 h-5 text-white\" />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-slate-900\">CABLYS</h1>\n                <p className=\"text-xs text-slate-500 -mt-1\">Cable Installation System</p>\n              </div>\n            </div>\n\n            {/* Navigation Desktop - allontanata dal logo */}\n            <div className=\"hidden md:flex items-center space-x-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href ||\n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n\n              // Gestione speciale per il dropdown Excel\n              if (item.hasDropdown && item.name === 'Gestione Excel') {\n                return (\n                  <div key={item.name} className=\"relative\" ref={dropdownRef}>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className={`flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent ${\n                        isActive ? 'bg-blue-100 text-blue-700' : 'text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200'\n                      }`}\n                      onClick={() => setExcelDropdownOpen(!excelDropdownOpen)}\n                    >\n                      <Icon className=\"w-4 h-4\" />\n                      <span className=\"hidden lg:inline\">{item.name}</span>\n                      <ChevronDown className=\"w-3 h-3\" />\n                    </Button>\n\n                    {excelDropdownOpen && (\n                      <div className=\"absolute top-full left-0 mt-1 w-48 bg-white border border-slate-200 rounded-md shadow-lg z-50\">\n                        <div className=\"py-1\">\n                          <Link\n                            href=\"/excel/import\"\n                            className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\"\n                            onClick={() => setExcelDropdownOpen(false)}\n                          >\n                            <div className=\"flex items-center space-x-2\">\n                              <Upload className=\"w-4 h-4\" />\n                              <span>Importa Excel</span>\n                            </div>\n                          </Link>\n                          <Link\n                            href=\"/excel/export\"\n                            className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\"\n                            onClick={() => setExcelDropdownOpen(false)}\n                          >\n                            <div className=\"flex items-center space-x-2\">\n                              <Download className=\"w-4 h-4\" />\n                              <span>Esporta Excel</span>\n                            </div>\n                          </Link>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )\n              }\n\n              return (\n                <Link key={item.name} href={item.href}>\n                  {isActive ? (\n                    <PrimaryButton\n                      size=\"sm\"\n                      className=\"flex items-center space-x-2\"\n                      icon={<Icon className=\"w-4 h-4\" />}\n                    >\n                      <span className=\"hidden lg:inline\">{item.name}</span>\n                    </PrimaryButton>\n                  ) : (\n                    <QuickButton\n                      size=\"sm\"\n                      className=\"flex items-center space-x-2 px-3 py-2 text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 ease-in-out rounded-md border border-transparent\"\n                    >\n                      <Icon className=\"w-4 h-4\" />\n                      <span className=\"hidden lg:inline\">{item.name}</span>\n                    </QuickButton>\n                  )}\n                </Link>\n              )\n            })}\n            </div>\n          </div>\n\n          {/* User Info a destra con più margine */}\n          <div className=\"flex items-center space-x-4 ml-8\">\n            {/* Display cantiere selezionato - versione compatta */}\n            {cantiereId && cantiereId > 0 && (\n              <div className=\"hidden sm:flex items-center space-x-2 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md\">\n                <Building2 className=\"w-3 h-3 text-blue-600\" />\n                <div className=\"text-xs\">\n                  <span className=\"text-blue-900 font-medium\">{cantiereName}</span>\n                </div>\n              </div>\n            )}\n\n            <div className=\"hidden sm:flex items-center space-x-2\">\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-slate-900\">\n                  {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}\n                  <span className=\"text-xs text-slate-500 ml-1\">\n                    ({user?.ruolo === 'owner' ? 'owner' : user?.ruolo || ''})\n                  </span>\n                </p>\n              </div>\n              <div className=\"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                <Users className=\"w-3 h-3 text-white\" />\n              </div>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={logout}\n                title={isImpersonating ? 'Torna al menu admin' : 'Logout'}\n                className=\"hover:bg-red-50 hover:text-red-600 transition-all duration-200 ease-in-out rounded-md\"\n              >\n                <LogOut className=\"w-4 h-4\" />\n              </Button>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 ease-in-out rounded-md\"\n              >\n                {isOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isOpen && (\n        <div className=\"md:hidden border-t border-slate-200 bg-white\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href || \n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n              \n              return (\n                <Link key={item.name} href={item.href}>\n                  {isActive ? (\n                    <PrimaryButton\n                      size=\"sm\"\n                      className=\"w-full justify-start space-x-3\"\n                      onClick={() => setIsOpen(false)}\n                      icon={<Icon className=\"w-4 h-4\" />}\n                    >\n                      {item.name}\n                    </PrimaryButton>\n                  ) : (\n                    <QuickButton\n                      size=\"sm\"\n                      className=\"w-full justify-start space-x-3 text-slate-600 hover:text-slate-900 hover:bg-blue-50 transition-all duration-200 ease-in-out\"\n                      onClick={() => setIsOpen(false)}\n                    >\n                      <Icon className=\"w-4 h-4\" />\n                      <span>{item.name}</span>\n                    </QuickButton>\n                  )}\n                </Link>\n              )\n            })}\n          </div>\n          \n          {/* Mobile User Info - versione compatta */}\n          <div className=\"border-t border-slate-200 px-4 py-3\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                {user ? <Users className=\"w-3 h-3 text-white\" /> : <Building2 className=\"w-3 h-3 text-white\" />}\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-slate-900\">\n                  {isImpersonating && impersonatedUser ? impersonatedUser.username : (user ? user.username : cantiere?.commessa)}\n                  <span className=\"text-xs text-slate-500 ml-1\">\n                    ({user?.ruolo === 'owner' ? 'owner' : user?.ruolo || 'Cantiere'})\n                  </span>\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;AA4BA,MAAM,gBAAgB,CAAC,UAA8B,iBAA0B,kBAAuB;IACpG,iEAAiE;IACjE,MAAM,aAAa;QACjB,MAAM,aAAa,UAAU,eACvB,aAAa,SAAS,mBACtB,aAAa,kBAAkB,kBAAkB;QACvD,MAAM,aAAa,UAAU,WACvB,aAAa,SAAS,cACtB,aAAa,kBAAkB,UAAU;QAC/C,MAAM,mMAAA,CAAA,OAAI;IACZ;IAEA,IAAI,aAAa,WAAW,CAAC,iBAAiB;QAC5C,uEAAuE;QACvE,OAAO;YAAC;SAAW;IACrB;IAEA,IAAI,aAAa,UAAW,mBAAmB,kBAAkB,SAAS,QAAS;QACjF,qEAAqE;QACrE,MAAM,MAAM;YAAC;SAAW;QACxB,IAAI,iBAAiB;YACnB,IAAI,IAAI,CAAC;gBAAE,MAAM;gBAAY,MAAM;gBAAa,MAAM,gNAAA,CAAA,YAAS;YAAC;QAClE;QAEA,wFAAwF;QACxF,IAAI,YAAY;YACd,IAAI,IAAI,CACN;gBAAE,MAAM;gBAAmB,MAAM;gBAAS,MAAM,oMAAA,CAAA,QAAK;YAAC,GACtD;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,wMAAA,CAAA,UAAO;YAAC,GACzD;gBAAE,MAAM;gBAAkB,MAAM;gBAAU,MAAM,8MAAA,CAAA,WAAQ;gBAAE,aAAa;YAAK,GAC5E;gBAAE,MAAM;gBAAU,MAAM;gBAAY,MAAM,kNAAA,CAAA,YAAS;YAAC,GACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAY,MAAM,wNAAA,CAAA,gBAAa;YAAC,GAClE;gBAAE,MAAM;gBAAkB,MAAM;gBAAiB,MAAM,0MAAA,CAAA,WAAQ;YAAC;QAEpE;QAEA,OAAO;IACT;IAEA,IAAI,aAAa,mBAAoB,mBAAmB,kBAAkB,SAAS,iBAAkB;QACnG,8DAA8D;QAC9D,MAAM,MAAM;YAAC;SAAW;QAExB,4DAA4D;QAC5D,IAAI,YAAY;YACd,2DAA2D;YAC3D,IAAI,aAAa,iBAAiB;gBAChC,IAAI,IAAI,CAAC;oBAAE,MAAM;oBAAmB,MAAM;oBAAS,MAAM,oMAAA,CAAA,QAAK;gBAAC;YACjE;YAEA,IAAI,IAAI,CACN;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,wMAAA,CAAA,UAAO;YAAC,GACzD;gBAAE,MAAM;gBAAkB,MAAM;gBAAU,MAAM,8MAAA,CAAA,WAAQ;gBAAE,aAAa;YAAK,GAC5E;gBAAE,MAAM;gBAAU,MAAM;gBAAY,MAAM,kNAAA,CAAA,YAAS;YAAC,GACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAY,MAAM,wNAAA,CAAA,gBAAa;YAAC,GAClE;gBAAE,MAAM;gBAAkB,MAAM;gBAAiB,MAAM,0MAAA,CAAA,WAAQ;YAAC;QAEpE;QAEA,OAAO;IACT;IAEA,UAAU;IACV,OAAO;QAAC;KAAW;AACrB;AAEO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE7F,wEAAwE;IACxE,MAAM,aAAa,UAAU,eAAe,CAAC,6EAA8F,CAAC;IAC5I,MAAM,eAAe,UAAU,YAAY,CAAC,6EAA+E,EAAE,KAAK,CAAC,SAAS,EAAE,YAAY;IAE1J,MAAM,aAAa,cAAc,MAAM,OAAO,iBAAiB,kBAAkB;IAEjF,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,mBAAmB,KAAiB;YAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,qBAAqB;YACvB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,4CAA4C;IAC5C,IAAI,aAAa,UAAU;QACzB,OAAO;IACT;IAEA,0CAA0C;IAC1C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;8CAKhD,8OAAC;oCAAI,WAAU;8CACd,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;wCAClE,MAAM,OAAO,KAAK,IAAI;wCAEtB,0CAA0C;wCAC1C,IAAI,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,kBAAkB;4CACtD,qBACE,8OAAC;gDAAoB,WAAU;gDAAW,KAAK;;kEAC7C,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAW,CAAC,mHAAmH,EAC7H,WAAW,8BAA8B,8EACzC;wDACF,SAAS,IAAM,qBAAqB,CAAC;;0EAErC,8OAAC;gEAAK,WAAU;;;;;;0EAChB,8OAAC;gEAAK,WAAU;0EAAoB,KAAK,IAAI;;;;;;0EAC7C,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;;oDAGxB,mCACC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,qBAAqB;8EAEpC,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,qBAAqB;8EAEpC,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAlCR,KAAK,IAAI;;;;;wCA0CvB;wCAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;4CAAiB,MAAM,KAAK,IAAI;sDAClC,yBACC,8OAAC,8IAAA,CAAA,gBAAa;gDACZ,MAAK;gDACL,WAAU;gDACV,oBAAM,8OAAC;oDAAK,WAAU;;;;;;0DAEtB,cAAA,8OAAC;oDAAK,WAAU;8DAAoB,KAAK,IAAI;;;;;;;;;;qEAG/C,8OAAC,8IAAA,CAAA,cAAW;gDACV,MAAK;gDACL,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAoB,KAAK,IAAI;;;;;;;;;;;;2CAfxC,KAAK,IAAI;;;;;oCAoBxB;;;;;;;;;;;;sCAKF,8OAAC;4BAAI,WAAU;;gCAEZ,cAAc,aAAa,mBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;8CAKnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;;oDACV,mBAAmB,mBAAmB,iBAAiB,QAAQ,GAAG,MAAM;kEACzE,8OAAC;wDAAK,WAAU;;4DAA8B;4DAC1C,MAAM,UAAU,UAAU,UAAU,MAAM,SAAS;4DAAG;;;;;;;;;;;;;;;;;;sDAI9D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,OAAO,kBAAkB,wBAAwB;4CACjD,WAAU;sDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,UAAU,CAAC;wCAC1B,WAAU;kDAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ/D,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;4BAClE,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CAClC,yBACC,8OAAC,8IAAA,CAAA,gBAAa;oCACZ,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;oCACzB,oBAAM,8OAAC;wCAAK,WAAU;;;;;;8CAErB,KAAK,IAAI;;;;;yDAGZ,8OAAC,8IAAA,CAAA,cAAW;oCACV,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;;sDAEzB,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;;;;;;+BAjBX,KAAK,IAAI;;;;;wBAsBxB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,qBAAO,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;6DAA0B,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAE1E,8OAAC;8CACC,cAAA,8OAAC;wCAAE,WAAU;;4CACV,mBAAmB,mBAAmB,iBAAiB,QAAQ,GAAI,OAAO,KAAK,QAAQ,GAAG,UAAU;0DACrG,8OAAC;gDAAK,WAAU;;oDAA8B;oDAC1C,MAAM,UAAU,UAAU,UAAU,MAAM,SAAS;oDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpF", "debugId": null}}]}