/**
 * TEST INTERFACCIA UTENTE
 * Verifica navigazione, form, dialog, responsive design e interazioni
 */

const puppeteer = require('puppeteer');

class UITester {
  constructor() {
    this.baseUrl = 'http://localhost:3000';
    this.browser = null;
    this.page = null;
    this.results = {
      navigation: { passed: 0, failed: 0, tests: [] },
      forms: { passed: 0, failed: 0, tests: [] },
      dialogs: { passed: 0, failed: 0, tests: [] },
      responsive: { passed: 0, failed: 0, tests: [] },
      interactions: { passed: 0, failed: 0, tests: [] }
    };
  }

  log(message, color = 'reset') {
    const colors = {
      green: '\x1b[32m',
      red: '\x1b[31m',
      yellow: '\x1b[33m',
      blue: '\x1b[34m',
      reset: '\x1b[0m',
      bold: '\x1b[1m'
    };
    console.log(`${colors[color]}${message}${colors.reset}`);
  }

  async setup() {
    try {
      this.log('\n🚀 Avvio browser per test UI...', 'blue');
      
      this.browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });
      
      this.page = await this.browser.newPage();
      
      // Imposta viewport standard
      await this.page.setViewport({ width: 1366, height: 768 });
      
      // Intercetta errori JavaScript
      this.page.on('pageerror', error => {
        this.log(`❌ Errore JavaScript: ${error.message}`, 'red');
      });
      
      // Intercetta errori di rete
      this.page.on('requestfailed', request => {
        this.log(`❌ Richiesta fallita: ${request.url()}`, 'red');
      });
      
      this.log('✅ Browser avviato', 'green');
      return true;
    } catch (error) {
      this.log(`❌ Errore setup browser: ${error.message}`, 'red');
      return false;
    }
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      this.log('✅ Browser chiuso', 'green');
    }
  }

  async login() {
    try {
      this.log('\n🔐 Esecuzione login...', 'blue');
      
      await this.page.goto(`${this.baseUrl}/login`, { waitUntil: 'networkidle0' });
      
      // Compila form di login
      await this.page.type('input[id="username"]', 'admin');
      await this.page.type('input[id="password"]', 'admin');
      
      // Clicca login
      await this.page.click('button[type="submit"]');
      
      // Aspetta redirect
      await this.page.waitForNavigation({ waitUntil: 'networkidle0' });
      
      // Verifica che siamo nella dashboard
      const url = this.page.url();
      if (url.includes('/admin') || url.includes('/dashboard') || url === `${this.baseUrl}/`) {
        this.log('✅ Login riuscito', 'green');
        return true;
      }
      
      throw new Error('Login fallito - redirect non corretto');
    } catch (error) {
      this.log(`❌ Errore login: ${error.message}`, 'red');
      return false;
    }
  }

  async testNavigation() {
    this.log('\n🧭 Test Navigazione', 'blue');
    this.log('='.repeat(50), 'blue');

    const navigationTests = [
      {
        name: 'Navigazione Admin',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          const title = await this.page.title();
          return title.length > 0;
        }
      },
      {
        name: 'Navigazione Cantieri',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/cantieri`, { waitUntil: 'networkidle0' });
          const hasContent = await this.page.$('main') !== null;
          return hasContent;
        }
      },
      {
        name: 'Navigazione Cavi',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/cavi`, { waitUntil: 'networkidle0' });
          const hasContent = await this.page.$('main') !== null;
          return hasContent;
        }
      },
      {
        name: 'Navigazione Parco Cavi',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/parco-cavi`, { waitUntil: 'networkidle0' });
          const hasContent = await this.page.$('main') !== null;
          return hasContent;
        }
      },
      {
        name: 'Navigazione Reports',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/reports`, { waitUntil: 'networkidle0' });
          const hasContent = await this.page.$('main') !== null;
          return hasContent;
        }
      }
    ];

    await this.runTestSuite(navigationTests, 'navigation');
  }

  async testForms() {
    this.log('\n📝 Test Form', 'blue');
    this.log('='.repeat(50), 'blue');

    const formTests = [
      {
        name: 'Form Login - Validazione campi vuoti',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/login`, { waitUntil: 'networkidle0' });
          
          // Clicca submit senza compilare
          await this.page.click('button[type="submit"]');
          
          // Verifica che non ci sia redirect (rimane sulla pagina login)
          await this.page.waitForTimeout(1000);
          const url = this.page.url();
          return url.includes('/login');
        }
      },
      {
        name: 'Form responsivi - Mobile viewport',
        test: async () => {
          await this.page.setViewport({ width: 375, height: 667 }); // iPhone SE
          await this.page.goto(`${this.baseUrl}/login`, { waitUntil: 'networkidle0' });
          
          const form = await this.page.$('form');
          const isVisible = await form.isIntersectingViewport();
          
          // Ripristina viewport
          await this.page.setViewport({ width: 1366, height: 768 });
          
          return isVisible;
        }
      },
      {
        name: 'Input accessibilità - Tab navigation',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/login`, { waitUntil: 'networkidle0' });
          
          // Naviga con Tab
          await this.page.keyboard.press('Tab');
          const firstInput = await this.page.evaluate(() => document.activeElement.tagName);
          
          await this.page.keyboard.press('Tab');
          const secondInput = await this.page.evaluate(() => document.activeElement.tagName);
          
          return firstInput === 'INPUT' && secondInput === 'INPUT';
        }
      }
    ];

    await this.runTestSuite(formTests, 'forms');
  }

  async testDialogs() {
    this.log('\n💬 Test Dialog', 'blue');
    this.log('='.repeat(50), 'blue');

    const dialogTests = [
      {
        name: 'Dialog apertura/chiusura',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          
          // Cerca un pulsante che apre dialog
          const buttons = await this.page.$$('button');
          if (buttons.length === 0) return true; // Nessun dialog da testare
          
          // Clicca primo pulsante
          await buttons[0].click();
          await this.page.waitForTimeout(500);
          
          // Verifica se si è aperto un dialog
          const dialog = await this.page.$('[role="dialog"]') || await this.page.$('.dialog');
          return dialog !== null;
        }
      },
      {
        name: 'Dialog ESC key',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          
          // Prova a premere ESC
          await this.page.keyboard.press('Escape');
          await this.page.waitForTimeout(300);
          
          // Se non ci sono errori, il test passa
          return true;
        }
      },
      {
        name: 'Dialog overlay click',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          
          // Cerca overlay
          const overlay = await this.page.$('[data-overlay]') || await this.page.$('.overlay');
          if (!overlay) return true; // Nessun overlay da testare
          
          await overlay.click();
          await this.page.waitForTimeout(300);
          
          return true;
        }
      }
    ];

    await this.runTestSuite(dialogTests, 'dialogs');
  }

  async testResponsive() {
    this.log('\n📱 Test Responsive Design', 'blue');
    this.log('='.repeat(50), 'blue');

    const responsiveTests = [
      {
        name: 'Mobile (375px) - Layout',
        test: async () => {
          await this.page.setViewport({ width: 375, height: 667 });
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          
          const body = await this.page.$('body');
          const isVisible = await body.isIntersectingViewport();
          
          return isVisible;
        }
      },
      {
        name: 'Tablet (768px) - Layout',
        test: async () => {
          await this.page.setViewport({ width: 768, height: 1024 });
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          
          const body = await this.page.$('body');
          const isVisible = await body.isIntersectingViewport();
          
          return isVisible;
        }
      },
      {
        name: 'Desktop (1366px) - Layout',
        test: async () => {
          await this.page.setViewport({ width: 1366, height: 768 });
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          
          const body = await this.page.$('body');
          const isVisible = await body.isIntersectingViewport();
          
          return isVisible;
        }
      }
    ];

    await this.runTestSuite(responsiveTests, 'responsive');
  }

  async testInteractions() {
    this.log('\n🖱️ Test Interazioni', 'blue');
    this.log('='.repeat(50), 'blue');

    const interactionTests = [
      {
        name: 'Click su pulsanti',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          
          const buttons = await this.page.$$('button');
          if (buttons.length === 0) return true;
          
          // Clicca primo pulsante disponibile
          await buttons[0].click();
          await this.page.waitForTimeout(300);
          
          return true; // Se non ci sono errori, il test passa
        }
      },
      {
        name: 'Hover effects',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          
          const buttons = await this.page.$$('button');
          if (buttons.length === 0) return true;
          
          // Hover su primo pulsante
          await buttons[0].hover();
          await this.page.waitForTimeout(200);
          
          return true;
        }
      },
      {
        name: 'Keyboard navigation',
        test: async () => {
          await this.page.goto(`${this.baseUrl}/admin`, { waitUntil: 'networkidle0' });
          
          // Naviga con Tab
          await this.page.keyboard.press('Tab');
          await this.page.keyboard.press('Tab');
          await this.page.keyboard.press('Enter');
          
          await this.page.waitForTimeout(300);
          return true;
        }
      }
    ];

    await this.runTestSuite(interactionTests, 'interactions');
  }

  async runTestSuite(tests, category) {
    for (const test of tests) {
      try {
        const startTime = Date.now();
        const passed = await test.test();
        const duration = Date.now() - startTime;
        
        if (passed) {
          this.log(`  ✅ ${test.name} (${duration}ms)`, 'green');
          this.results[category].passed++;
        } else {
          this.log(`  ❌ ${test.name} - Test fallito`, 'red');
          this.results[category].failed++;
        }
        
        this.results[category].tests.push({
          name: test.name,
          passed,
          duration
        });
      } catch (error) {
        this.log(`  ❌ ${test.name} - ${error.message}`, 'red');
        this.results[category].failed++;
        this.results[category].tests.push({
          name: test.name,
          passed: false,
          error: error.message
        });
      }
    }
  }

  printSummary() {
    this.log('\n📊 RIEPILOGO TEST UI', 'bold');
    this.log('='.repeat(60), 'blue');
    
    const categories = [
      { name: 'Navigazione', results: this.results.navigation },
      { name: 'Form', results: this.results.forms },
      { name: 'Dialog', results: this.results.dialogs },
      { name: 'Responsive', results: this.results.responsive },
      { name: 'Interazioni', results: this.results.interactions }
    ];
    
    let totalPassed = 0;
    let totalFailed = 0;
    
    categories.forEach(category => {
      const { passed, failed } = category.results;
      const total = passed + failed;
      const percentage = total > 0 ? Math.round((passed / total) * 100) : 0;
      
      this.log(`\n${category.name}: ${passed}/${total} (${percentage}%)`, 
        percentage >= 80 ? 'green' : percentage >= 60 ? 'yellow' : 'red');
      
      totalPassed += passed;
      totalFailed += failed;
    });
    
    const grandTotal = totalPassed + totalFailed;
    const overallPercentage = grandTotal > 0 ? Math.round((totalPassed / grandTotal) * 100) : 0;
    
    this.log(`\n🎯 TOTALE: ${totalPassed}/${grandTotal} (${overallPercentage}%)`, 
      overallPercentage >= 80 ? 'green' : overallPercentage >= 60 ? 'yellow' : 'red');
    
    if (overallPercentage >= 80) {
      this.log('\n✅ Interfaccia utente funziona correttamente!', 'green');
    } else if (overallPercentage >= 60) {
      this.log('\n⚠️  Interfaccia ha alcuni problemi da risolvere', 'yellow');
    } else {
      this.log('\n❌ Interfaccia ha problemi critici', 'red');
    }
  }

  async run() {
    this.log('\n🚀 Test Interfaccia Utente', 'bold');
    this.log('Verifica navigazione, form, dialog, responsive e interazioni', 'blue');
    
    if (!(await this.setup())) {
      this.log('\n❌ Impossibile avviare browser', 'red');
      return;
    }
    
    try {
      if (!(await this.login())) {
        this.log('\n❌ Impossibile procedere senza login', 'red');
        return;
      }
      
      await this.testNavigation();
      await this.testForms();
      await this.testDialogs();
      await this.testResponsive();
      await this.testInteractions();
      
      this.printSummary();
    } finally {
      await this.cleanup();
    }
  }
}

// Esecuzione dei test
if (require.main === module) {
  const tester = new UITester();
  tester.run().catch(error => {
    console.error('Errore fatale:', error);
    process.exit(1);
  });
}

module.exports = UITester;
