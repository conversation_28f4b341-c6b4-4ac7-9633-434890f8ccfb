'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, ClipboardList, Users } from 'lucide-react'
import { comandeApi, responsabiliApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface CreaComandaDialogProps {
  open: boolean
  console.log({
  open: boolean
  onClose: () => void
  caviSelezionati: string[]
  tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'
  console.log({
  tipoComanda?: 'POSA' | 'COLLEGAMENTO_PARTENZA' | 'COLLEGAMENTO_ARRIVO' | 'CERTIFICAZIONE'
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface Responsabile {
  id: number
  console.log({
  id: number
  nome_responsabile: string
  numero_telefono?: string
  mail?: string
}

export default function CreaComandaDialog({
  open,
  onClose,
  caviSelezionati,
  tipoComanda,
  onSuccess,
  onError
}: CreaComandaDialogProps) {
  const { cantiere } = useAuth()
  const [formData, setFormData] = useState({
    tipo_comanda: tipoComanda || 'POSA',
    console.log({
    tipo_comanda: tipoComanda || 'POSA',
    responsabile: '',
    note: ''
  })
  const [responsabili, setResponsabili] = useState<Responsabile[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingResponsabili, setLoadingResponsabili] = useState(false)
  const [error, setError] = useState('')

  // Reset form quando si apre il dialog
  useEffect(() => {
    if (open) {
      setFormData({
        tipo_comanda: tipoComanda || 'POSA',
        console.log({
        tipo_comanda: tipoComanda || 'POSA',
        responsabile: '',
        note: ''
      })
      setError('')
      loadResponsabili()
    }
  }, [open, tipoComanda])

  const loadResponsabili = async () => {
    if (!cantiere) return

    try {
      setLoadingResponsabili(true)
      const response = await responsabiliApi.getResponsabili(cantiere.id_cantiere)
      setResponsabili(response.data)
    } catch (error) {
      setResponsabili([])
    } finally {
      setLoadingResponsabili(false)
    }
  }

  const getTipoComandaLabel = (tipo: string) => {
    switch (tipo) {
      case 'POSA':
        return 'Posa Cavi'
      case 'COLLEGAMENTO_PARTENZA':
        return 'Collegamento Partenza'
      case 'COLLEGAMENTO_ARRIVO':
        return 'Collegamento Arrivo'
      case 'CERTIFICAZIONE':
        return 'Certificazione'
      default:
        return tipo
    }
  }

  const handleCreaComanda = async () => {
    if (!cantiere) return

    if (!formData.responsabile) {
      setError('Seleziona un responsabile per la comanda')
      return
    }

    if (caviSelezionati.length === 0) {
      setError('Seleziona almeno un cavo per la comanda')
      return
    }

    try {
      setLoading(true)
      setError('')

      // Crea la comanda con i cavi assegnati
      const comandaData = {
        tipo_comanda: formData.tipo_comanda,
        console.log({
        tipo_comanda: formData.tipo_comanda,
        responsabile: formData.responsabile,
        note: formData.note || null
      }

      const response = await comandeApi.createComandaWithCavi(
        cantiere.id_cantiere,
        comandaData,
        caviSelezionati
      )

      onSuccess(`Comanda ${response.data.codice_comanda} creata con successo per ${caviSelezionati.length} cavi`)
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante la creazione della comanda'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ClipboardList className="h-5 w-5" />
            Crea Nuova Comanda
          </DialogTitle>
          <DialogDescription>
            Crea una nuova comanda per {caviSelezionati.length} cavi selezionati
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Cavi selezionati */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <Label className="text-sm font-medium">Cavi Selezionati ({caviSelezionati.length})</Label>
            <div className="mt-2 max-h-32 overflow-y-auto">
              <div className="flex flex-wrap gap-1">
                {caviSelezionati.slice(0, 10).map((cavoId) => (
                  <span
                    key={cavoId}
                    className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                  >
                    {cavoId}
                  </span>
                ))}
                {caviSelezionati.length > 10 && (
                  <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                    +{caviSelezionati.length - 10} altri...
                  </span>
                )}
              </div>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Tipo comanda */}
          <div className="space-y-2">
            <Label htmlFor="tipo">Tipo Comanda *</Label>
            <Select
              value={formData.tipo_comanda}
              onValueChange={(value) => setFormData(prev => ({ ...prev, tipo_comanda: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="POSA">🔧 Posa Cavi</SelectItem>
                <SelectItem value="COLLEGAMENTO_PARTENZA">🔌 Collegamento Partenza</SelectItem>
                <SelectItem value="COLLEGAMENTO_ARRIVO">⚡ Collegamento Arrivo</SelectItem>
                <SelectItem value="CERTIFICAZIONE">📋 Certificazione</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Responsabile */}
          <div className="space-y-2">
            <Label htmlFor="responsabile">Responsabile *</Label>
            <Select
              value={formData.responsabile}
              onValueChange={(value) => setFormData(prev => ({ ...prev, responsabile: value }))}
              disabled={loadingResponsabili}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleziona responsabile..." />
              </SelectTrigger>
              <SelectContent>
                {responsabili.map((resp) => (
                  <SelectItem key={resp.id} value={resp.nome_responsabile}>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>{resp.nome_responsabile}</span>
                      {resp.numero_telefono && (
                        <span className="text-xs text-gray-500">- {resp.numero_telefono}</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Note */}
          <div className="space-y-2">
            <Label htmlFor="note">Note (opzionale)</Label>
            <Textarea
              id="note"
              placeholder="Inserisci eventuali note per la comanda..."
              value={formData.note}
              onChange={(e) => setFormData(prev => ({ ...prev, note: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Riepilogo */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <Label className="text-sm font-medium">Riepilogo Comanda</Label>
            <div className="mt-2 space-y-1 text-sm">
              <div><strong>Tipo:</strong> {getTipoComandaLabel(formData.tipo_comanda)}</div>
              <div><strong>Responsabile:</strong> {formData.responsabile || 'Non selezionato'}</div>
              <div><strong>Cavi:</strong> {caviSelezionati.length} selezionati</div>
              {formData.note && <div><strong>Note:</strong> {formData.note}</div>}
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Annulla
          </Button>
          <Button
            onClick={handleCreaComanda}
            disabled={loading || !formData.responsabile || caviSelezionati.length === 0}
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <ClipboardList className="h-4 w-4 mr-2" />}
            Crea Comanda
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
