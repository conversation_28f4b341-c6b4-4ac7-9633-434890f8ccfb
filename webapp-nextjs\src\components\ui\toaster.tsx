'use client'

import { useToast } from '@/hooks/use-toast'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { X, CheckCircle, AlertCircle } from 'lucide-react'

export function Toaster() {
  const { toasts, dismiss } = useToast()

  return (
    <div className="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className="group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full"
          style={{
            backgroundColor: toast.variant === 'destructive' ? '#fef2f2' : '#f0f9ff'
          }}
        >
          <div className="flex items-start space-x-2">
            {toast.variant === 'destructive' ? (
              <AlertCircle className="h-4 w-4 text-red-600 mt-0.5" />
            ) : (
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
            )}
            <div className="grid gap-1">
              {toast.title && (
                <div className={`text-sm font-semibold ${
                  toast.variant === 'destructive' ? 'text-red-900' : 'text-gray-900'
                }`}>
                  {toast.title}
                </div>
              )}
              {toast.description && (
                <div className={`text-sm ${
                  toast.variant === 'destructive' ? 'text-red-700' : 'text-gray-700'
                }`}>
                  {toast.description}
                </div>
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1 h-6 w-6 p-0 hover:bg-transparent"
            onClick={() => dismiss(toast.id)}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      ))}
    </div>
  )
}
