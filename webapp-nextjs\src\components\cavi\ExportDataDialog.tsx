'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Download, FileSpreadsheet, Database } from 'lucide-react'
import { excelApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'

interface ExportDataDialogProps {
  open: boolean
  }

export default function ExportDataDialog({
  open,
  onClose,
  onSuccess,
  onError
}: ExportDataDialogProps) {
  const { cantiere } = useAuth()
  const [selectedExports, setSelectedExports] = useState({
    cavi: true)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleExportChange = (exportType: string, checked: boolean) => {
    setSelectedExports(prev => ({
      ...prev,
      [exportType]: checked
    }))
  }

  const handleExportCavi = async () => {
    if (!cantiere) return

    try {
      setLoading(true)
      const response = await excelApi.exportCavi(cantiere.id_cantiere)
      
      // Crea un link per il download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `cavi_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      onSuccess('Export cavi completato con successo')
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\'export dei cavi'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleExportBobine = async () => {
    if (!cantiere) return

    try {
      setLoading(true)
      const response = await excelApi.exportBobine(cantiere.id_cantiere)
      
      // Crea un link per il download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `bobine_${cantiere.nome_cantiere}_${new Date().toISOString().split('T')[0]}.xlsx`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      onSuccess('Export bobine completato con successo')
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\'export delle bobine'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleExportAll = async () => {
    if (!cantiere) return

    try {
      setLoading(true)
      setError('')

      const exports = []

      if (selectedExports.cavi) {
        exports.push(handleExportCavi())
      }

      if (selectedExports.bobine) {
        exports.push(handleExportBobine())
      }

      // TODO: Implementare export per comande, certificazioni, responsabili
      if (selectedExports.comande) {
      }

      if (selectedExports.certificazioni) {
      }

      if (selectedExports.responsabili) {
      }

      await Promise.all(exports)

      const exportCount = Object.values(selectedExports).filter(Boolean).length
      onSuccess(`Export completato: ${exportCount} file scaricati`)
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante l\'export dei dati'
      onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const getSelectedCount = () => {
    return Object.values(selectedExports).filter(Boolean).length
  }

  const exportOptions = [
    {
      key: 'cavi',
    {
      key: 'bobine',
    {
      key: 'comande',
    {
      key: 'certificazioni',
    {
      key: 'responsabili'
  ]

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Esporta Dati Cantiere
          </DialogTitle>
          <DialogDescription>
            Seleziona i dati da esportare dal cantiere {cantiere?.nome_cantiere}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Opzioni di export */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Seleziona Dati da Esportare</Label>
            
            {exportOptions.map((option) => (
              <div
                key={option.key}
                className={`flex items-start space-x-3 p-3 rounded-lg border ${
                  option.available ? 'bg-white' : 'bg-gray-50'
                }`}
              >
                <Checkbox
                  id={option.key}
                  checked={selectedExports[option.key as keyof typeof selectedExports]}
                  onCheckedChange={(checked) => handleExportChange(option.key, checked as boolean)}
                  disabled={!option.available || loading}
                />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    {option.icon}
                    <Label
                      htmlFor={option.key}
                      className={`font-medium ${!option.available ? 'text-gray-500' : ''}`}
                    >
                      {option.label}
                      {!option.available && (
                        <span className="ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded">
                          In sviluppo
                        </span>
                      )}
                    </Label>
                  </div>
                  <p className={`text-sm mt-1 ${!option.available ? 'text-gray-400' : 'text-gray-600'}`}>
                    {option.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Informazioni export */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <Label className="text-sm font-medium">Informazioni Export</Label>
            <ul className="mt-2 space-y-1 text-sm text-gray-600">
              <li>• I file saranno scaricati in formato Excel (.xlsx)</li>
              <li>• I nomi file includeranno data e nome cantiere</li>
              <li>• I dati esportati riflettono lo stato attuale del database</li>
              <li>• L'export non modifica i dati originali</li>
            </ul>
          </div>

          {/* Riepilogo */}
          {getSelectedCount() > 0 && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <Label className="text-sm font-medium">Riepilogo Export</Label>
              <div className="mt-2 space-y-1 text-sm">
                <div><strong>Cantiere:</strong> {cantiere?.nome_cantiere}</div>
                <div><strong>File da scaricare:</strong> {getSelectedCount()}</div>
                <div><strong>Data export:</strong> {new Date().toLocaleDateString('it-IT')}</div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Annulla
          </Button>
          <Button
            onClick={handleExportAll}
            disabled={loading || getSelectedCount() === 0}
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Download className="h-4 w-4 mr-2" />}
            Esporta {getSelectedCount() > 0 ? `(${getSelectedCount()})` : ''}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
